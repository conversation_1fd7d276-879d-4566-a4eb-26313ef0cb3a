"""
Strategie module voor de Prachtige Trading Bot.
Beheert de handelsstrategieën en signaalgeneratie.
"""

import logging
from typing import Dict, Any, List, Optional, Callable, Awaitable
from abc import ABC, abstractmethod
import asyncio
import pandas as pd
import numpy as np
import ta
from datetime import datetime, timedelta
import json

logger = logging.getLogger(__name__)

# Type aliassen
Signal = Dict[str, Any]
DataFrame = pd.DataFrame

class Strategy(ABC):
    """Abstracte basisklasse voor handelsstrategieën."""

    def __init__(self, name: str, params: Dict[str, Any] = None):
        """Initialiseer een strategie.

        Args:
            name: <PERSON><PERSON> van de strategie
            params: Parameters voor de strategie
        """
        self.name = name
        self.params = params or {}
        self.is_running = False
        self.callbacks = {
            'on_signal': [],
            'on_error': []
        }

    def on(self, event: str, callback: Callable[[Dict], Awaitable[None]]):
        """Registreer een callback voor een gebeurtenis."""
        if event in self.callbacks:
            self.callbacks[event].append(callback)
        return self

    async def _emit(self, event: str, data: Dict = None):
        """Roep alle callbacks voor een gebeurtenis aan."""
        data = data or {}
        data['strategy'] = self.name
        data['timestamp'] = datetime.utcnow().isoformat()

        if event in self.callbacks:
            for callback in self.callbacks[event]:
                try:
                    await callback(data)
                except Exception as e:
                    logger.error(f"Fout in {event} callback: {e}", exc_info=True)

    @abstractmethod
    async def initialize(self):
        """Initialiseer de strategie."""
        pass

    @abstractmethod
    async def start(self):
        """Start de strategie."""
        self.is_running = True

    @abstractmethod
    async def stop(self):
        """Stop de strategie."""
        self.is_running = False

    @abstractmethod
    async def analyze(self, data: DataFrame) -> Optional[Signal]:
        """Analyseer marktgegevens en genereer een signaal indien van toepassing.

        Args:
            data: Pandas DataFrame met marktgegevens

        Returns:
            Een signaal dictionary of None als er geen signaal is
        """
        pass

    @abstractmethod
    def get_required_columns(self) -> List[str]:
        """Geef de benodigde kolommen voor deze strategie."""
        return []


class MovingAverageCrossover(Strategy):
    """Eenvoudige Moving Average Crossover strategie."""

    def __init__(self, params: Dict[str, Any] = None):
        default_params = {
            'fast_ma': 10,
            'slow_ma': 30,
            'rsi_period': 14,
            'rsi_overbought': 70,
            'rsi_oversold': 30,
            'symbol': 'BTC/USDT:USDT',
            'timeframe': '1h'
        }

        # Update met meegegeven parameters
        if params:
            default_params.update(params)

        super().__init__('MovingAverageCrossover', default_params)

    async def initialize(self):
        """Initialiseer de strategie."""
        logger.info(f"Initialiseren {self.name} strategie met parameters: {self.params}")
        self.is_initialized = True

    async def start(self):
        """Start de strategie."""
        await super().start()
        logger.info(f"{self.name} strategie gestart")

    async def stop(self):
        """Stop de strategie."""
        await super().stop()
        logger.info(f"{self.name} strategie gestopt")

    def get_required_columns(self) -> List[str]:
        """Geef de benodigde kolommen voor deze strategie."""
        return ['open', 'high', 'low', 'close', 'volume']

    async def analyze(self, data: DataFrame) -> Optional[Signal]:
        """Analyseer marktgegevens en genereer een signaal."""
        if not self.is_running:
            return None

        try:
            # Maak een kopie om waarschuwingen te voorkomen
            df = data.copy()

            # Bereken indicatoren
            df['fast_ma'] = ta.trend.sma_indicator(
                df['close'], window=self.params['fast_ma']
            )
            df['slow_ma'] = ta.trend.sma_indicator(
                df['close'], window=self.params['slow_ma']
            )

            # Bereken RSI
            df['rsi'] = ta.momentum.RSIIndicator(
                df['close'], window=self.params['rsi_period']
            ).rsi()

            # Bepaal signalen
            last_row = df.iloc[-1]
            prev_row = df.iloc[-2]

            # Bepaal kruisingen
            golden_cross = (
                prev_row['fast_ma'] <= prev_row['slow_ma'] and
                last_row['fast_ma'] > last_row['slow_ma']
            )

            death_cross = (
                prev_row['fast_ma'] >= prev_row['slow_ma'] and
                last_row['fast_ma'] < last_row['slow_ma']
            )

            # Genereer signaal
            signal = {
                'symbol': self.params['symbol'],
                'timeframe': self.params['timeframe'],
                'timestamp': datetime.utcnow().isoformat(),
                'indicators': {
                    'fast_ma': last_row['fast_ma'],
                    'slow_ma': last_row['slow_ma'],
                    'rsi': last_row['rsi']
                },
                'recommendation': 'hold',
                'confidence': 0.0,
                'metadata': {}
            }

            # Bepaal aanbeveling op basis van kruisingen en RSI
            if golden_cross and last_row['rsi'] < self.params['rsi_overbought']:
                signal.update({
                    'recommendation': 'buy',
                    'confidence': 0.7,
                    'metadata': {
                        'reason': 'Golden cross gevonden met RSI niet overbought',
                        'entry_price': float(last_row['close']),
                        'take_profit': float(last_row['close'] * 1.02),  # 2% take profit
                        'stop_loss': float(last_row['close'] * 0.99),    # 1% stop loss
                    }
                })
            elif death_cross and last_row['rsi'] > self.params['rsi_oversold']:
                signal.update({
                    'recommendation': 'sell',
                    'confidence': 0.7,
                    'metadata': {
                        'reason': 'Death cross gevonden met RSI niet oversold',
                        'exit_price': float(last_row['close'])
                    }
                })

            # Alleen een signaal retourneren als het niet 'hold' is
            if signal['recommendation'] != 'hold':
                await self._emit('on_signal', signal)
                return signal

            return None

        except Exception as e:
            error_msg = f"Fout in {self.name} analyse: {str(e)}"
            logger.error(error_msg, exc_info=True)
            await self._emit('on_error', {'error': error_msg})
            return None


class StrategyManager:
    """Beheert meerdere handelsstrategieën."""

    def __init__(self, exchange_manager=None, database=None):
        """Initialiseer de strategy manager.

        Args:
            exchange_manager: ExchangeManager instantie
            database: DatabaseManager instantie
        """
        self.strategies: Dict[str, Strategy] = {}
        self.exchange_manager = exchange_manager
        self.database = database
        self.initialized = False
        self.is_running = False

    async def initialize(self):
        """Initialiseer de strategy manager."""
        if self.initialized:
            return

        try:
            # Laad strategieën uit de database indien beschikbaar
            if self.database:
                strategies_data = await self.database.fetch_all(
                    "SELECT * FROM strategies WHERE is_active = 1"
                )

                for strategy_data in strategies_data:
                    try:
                        strategy_class = self._get_strategy_class(strategy_data['name'])
                        if strategy_class:
                            params = json.loads(strategy_data['params']) if strategy_data['params'] else {}
                            strategy = strategy_class(params)
                            await self.add_strategy(strategy)
                            logger.info(f"Strategie geladen: {strategy.name}")
                    except Exception as e:
                        logger.error(f"Fout bij laden strategie {strategy_data['name']}: {e}")

            self.initialized = True
            logger.info("Strategy Manager geïnitialiseerd")
            return True

        except Exception as e:
            logger.error(f"Fout bij initialiseren Strategy Manager: {e}", exc_info=True)
            raise

    def _get_strategy_class(self, name: str):
        """Haal een strategie klasse op bij naam."""
        strategies = {
            'MovingAverageCrossover': MovingAverageCrossover,
            # Voeg hier meer strategieën toe
        }
        return strategies.get(name)

    async def add_strategy(self, strategy: Strategy):
        """Voeg een strategie toe aan de manager."""
        if not isinstance(strategy, Strategy):
            raise ValueError("Ongeldig strategie object")

        if strategy.name in self.strategies:
            raise ValueError(f"Strategie met naam {strategy.name} bestaat al")

        # Initialiseer de strategie
        await strategy.initialize()

        # Registreer standaard callbacks
        strategy.on('on_signal', self._on_strategy_signal)
        strategy.on('on_error', self._on_strategy_error)

        # Voeg toe aan actieve strategieën
        self.strategies[strategy.name] = strategy

        # Start de strategie als de manager actief is
        if self.is_running:
            await strategy.start()

        logger.info(f"Strategie toegevoegd: {strategy.name}")

    async def remove_strategy(self, name: str):
        """Verwijder een strategie van de manager."""
        if name not in self.strategies:
            raise ValueError(f"Onbekende strategie: {name}")

        # Stop de strategie
        strategy = self.strategies[name]
        if strategy.is_running:
            await strategy.stop()

        # Verwijder uit actieve strategieën
        del self.strategies[name]
        logger.info(f"Strategie verwijderd: {name}")

    async def start(self):
        """Start alle strategieën."""
        if self.is_running:
            return

        for name, strategy in self.strategies.items():
            try:
                if not strategy.is_running:
                    await strategy.start()
                    logger.info(f"Strategie gestart: {name}")
            except Exception as e:
                logger.error(f"Fout bij starten strategie {name}: {e}", exc_info=True)

        self.is_running = True
        logger.info("Strategy Manager gestart")

    async def stop(self):
        """Stop alle strategieën."""
        if not self.is_running:
            return

        for name, strategy in self.strategies.items():
            try:
                if strategy.is_running:
                    await strategy.stop()
                    logger.info(f"Strategie gestopt: {name}")
            except Exception as e:
                logger.error(f"Fout bij stoppen strategie {name}: {e}", exc_info=True)

        self.is_running = False
        logger.info("Strategy Manager gestopt")

    async def analyze_market(self, symbol: str, timeframe: str, data: DataFrame) -> List[Signal]:
        """Analyseer marktgegevens met alle relevante strategieën.

        Args:
            symbol: Trading paar (bijv. 'BTC/USDT')
            timeframe: Tijdsframe (bijv. '1h', '4h', '1d')
            data: Pandas DataFrame met OHLCV gegevens

        Returns:
            Lijst van gegenereerde signalen
        """
        signals = []

        for name, strategy in self.strategies.items():
            if not strategy.is_running:
                continue

            try:
                # Controleer of deze strategie van toepassing is op dit paar en timeframe
                if (strategy.params.get('symbol') == symbol and
                    strategy.params.get('timeframe') == timeframe):

                    # Voer analyse uit
                    signal = await strategy.analyze(data)
                    if signal:
                        signal['strategy'] = name
                        signals.append(signal)

            except Exception as e:
                logger.error(f"Fout in analyse {name} voor {symbol} {timeframe}: {e}", exc_info=True)

        return signals

    async def _on_strategy_signal(self, signal: Dict):
        """Verwerk een signaal van een strategie."""
        try:
            # Log het signaal
            logger.info(f"Signaal ontvangen: {signal}")

            # Sla het signaal op in de database indien beschikbaar
            if self.database:
                await self.database.execute(
                    """
                    INSERT INTO signals (
                        strategy, symbol, timeframe, recommendation, confidence,
                        indicators, metadata, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """,
                    (
                        signal.get('strategy'),
                        signal.get('symbol'),
                        signal.get('timeframe'),
                        signal.get('recommendation'),
                        signal.get('confidence', 0.0),
                        json.dumps(signal.get('indicators', {})),
                        json.dumps(signal.get('metadata', {})),
                        signal.get('timestamp', datetime.utcnow().isoformat())
                    )
                )

            # Voer hier verdere acties uit op basis van het signaal
            # Bijvoorbeeld: plaats een order via de exchange manager

        except Exception as e:
            logger.error(f"Fout bij verwerken signaal: {e}", exc_info=True)

    async def _on_strategy_error(self, error: Dict):
        """Verwerk een foutmelding van een strategie."""
        logger.error(f"Strategie fout: {error}")

        # Stuur een melding naar beheerders indien nodig
        # Bijvoorbeeld via e-mail of een ander notificatiekanaal

# Aliases voor backwards compatibility
MovingAverageStrategy = MovingAverageCrossover

# Export belangrijke klassen
__all__ = [
    'Strategy',
    'MovingAverageCrossover',
    'MovingAverageStrategy',
    'StrategyManager'
]
