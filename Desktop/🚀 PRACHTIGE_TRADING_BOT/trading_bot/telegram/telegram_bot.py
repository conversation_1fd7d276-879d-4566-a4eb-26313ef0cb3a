"""
🤖 TELEGRAM BOT - Main Telegram Bot Class
========================================

Hoofdklasse voor de Telegram bot integratie.
"""

import logging
from typing import List, Optional, Any
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, MessageHandler, filters

logger = logging.getLogger(__name__)

class TelegramBot:
    """Hoofdklasse voor de Telegram bot."""
    
    def __init__(self, token: str, admin_user_ids: List[int], 
                 exchange_manager=None, strategy_manager=None):
        """
        Initialiseer de Telegram bot.
        
        Args:
            token: Telegram bot token
            admin_user_ids: Lijst van admin user IDs
            exchange_manager: Exchange manager instance
            strategy_manager: Strategy manager instance
        """
        self.token = token
        self.admin_user_ids = admin_user_ids
        self.exchange_manager = exchange_manager
        self.strategy_manager = strategy_manager
        self.application: Optional[Application] = None
        
        logger.info("🤖 TelegramBot geïnitialiseerd")
    
    async def initialize(self) -> Application:
        """Initialiseer de Telegram application."""
        try:
            # Maak de Application
            self.application = Application.builder().token(self.token).build()
            
            # Voeg handlers toe
            await self._setup_handlers()
            
            logger.info("✅ Telegram bot application geïnitialiseerd")
            return self.application
            
        except Exception as e:
            logger.error(f"❌ Fout bij initialiseren Telegram bot: {e}")
            raise
    
    async def _setup_handlers(self):
        """Setup alle command en callback handlers."""
        try:
            # Import handlers
            from bot_telegram.telegram.handlers import (
                start_handler, help_handler, register_handler, 
                trading_handler, admin_handler, callback_handler, 
                message_handler, set_bot_manager
            )
            
            # Setup bot manager reference (dit wordt later ingesteld)
            # set_bot_manager(bot_manager)  # Dit wordt vanuit main.py gedaan
            
            # Voeg command handlers toe
            self.application.add_handler(CommandHandler("start", start_handler))
            self.application.add_handler(CommandHandler("help", help_handler))
            self.application.add_handler(CommandHandler("register", register_handler))
            self.application.add_handler(CommandHandler("trading", trading_handler))
            self.application.add_handler(CommandHandler("admin", admin_handler))
            
            # Voeg callback query handler toe
            self.application.add_handler(CallbackQueryHandler(callback_handler))
            
            # Voeg message handler toe
            self.application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, message_handler))
            
            # Voeg error handler toe
            self.application.add_error_handler(self._error_handler)
            
            logger.info("✅ Telegram handlers geconfigureerd")
            
        except Exception as e:
            logger.error(f"❌ Fout bij setup handlers: {e}")
            raise
    
    async def _error_handler(self, update, context):
        """Handle errors die optreden tijdens bot operatie."""
        logger.error(f"❌ Telegram bot error: {context.error}")
        
        # Stuur een vriendelijk bericht naar de gebruiker
        if update and update.effective_chat:
            try:
                await context.bot.send_message(
                    chat_id=update.effective_chat.id,
                    text="🤖 Er is een technische fout opgetreden. Ons team is op de hoogte gesteld. Probeer het later opnieuw."
                )
            except Exception as send_error:
                logger.error(f"❌ Kon geen error bericht sturen: {send_error}")
    
    async def send_message_to_admins(self, message: str):
        """Stuur een bericht naar alle admin gebruikers."""
        if not self.application:
            logger.warning("⚠️ Application niet geïnitialiseerd")
            return
        
        sent_count = 0
        for admin_id in self.admin_user_ids:
            try:
                await self.application.bot.send_message(
                    chat_id=admin_id,
                    text=message,
                    parse_mode='Markdown'
                )
                sent_count += 1
            except Exception as e:
                logger.warning(f"⚠️ Kon bericht niet sturen naar admin {admin_id}: {e}")
        
        logger.info(f"📤 Admin bericht verstuurd naar {sent_count}/{len(self.admin_user_ids)} admins")
        return sent_count
    
    async def broadcast_message(self, message: str, user_ids: List[int]):
        """Broadcast een bericht naar een lijst van gebruikers."""
        if not self.application:
            logger.warning("⚠️ Application niet geïnitialiseerd")
            return 0, 0
        
        sent_count = 0
        failed_count = 0
        
        for user_id in user_ids:
            try:
                await self.application.bot.send_message(
                    chat_id=user_id,
                    text=message,
                    parse_mode='Markdown'
                )
                sent_count += 1
            except Exception as e:
                logger.warning(f"⚠️ Kon bericht niet sturen naar user {user_id}: {e}")
                failed_count += 1
        
        logger.info(f"📤 Broadcast voltooid: {sent_count} geslaagd, {failed_count} gefaald")
        return sent_count, failed_count
