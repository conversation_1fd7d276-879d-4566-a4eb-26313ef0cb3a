""
Hoofdmodule voor de Prachtige Trading Bot.
Beheert de hoofdlogica en coördinatie van de trading bot.
"""
import logging
from typing import Dict, Any, Optional
import asyncio
from telegram.ext import Application
from config.config import (
    TELEGRAM_BOT_TOKEN, TELEGRAM_ADMIN_USER_IDS, LOG_LEVEL
)
from .exchanges import ExchangeManager
from .strategies import StrategyManager
from .database import DatabaseManager
from .telegram import TelegramBot

logger = logging.getLogger(__name__)

class TradingBot:
    """Hoofdklasse voor de trading bot."""

    def __init__(self):
        """Initialiseer de trading bot."""
        self.running = False
        self.exchange_manager: Optional[ExchangeManager] = None
        self.strategy_manager: Optional[StrategyManager] = None
        self.database: Optional[DatabaseManager] = None
        self.telegram_bot: Optional[TelegramBot] = None
        self.telegram_app: Optional[Application] = None

        # Configureer logging
        self._setup_logging()

    def _setup_logging(self):
        """Configureer logging."""
        logging.basicConfig(
            level=getattr(logging, LOG_LEVEL),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('trading_bot.log')
            ]
        )

    async def initialize(self):
        """Initialiseer alle componenten van de bot."""
        logger.info("Initialiseer Trading Bot...")

        try:
            # Initialiseer database
            self.database = DatabaseManager()
            await self.database.initialize()

            # Initialiseer exchange manager
            self.exchange_manager = ExchangeManager()
            await self.exchange_manager.initialize()

            # Initialiseer strategy manager
            self.strategy_manager = StrategyManager(
                exchange_manager=self.exchange_manager,
                database=self.database
            )
            await self.strategy_manager.initialize()

            # Initialiseer Telegram bot
            self.telegram_bot = TelegramBot(
                token=TELEGRAM_BOT_TOKEN,
                admin_user_ids=TELEGRAM_ADMIN_USER_IDS,
                exchange_manager=self.exchange_manager,
                strategy_manager=self.strategy_manager
            )
            self.telegram_app = await self.telegram_bot.initialize()

            logger.info("Trading Bot succesvol geïnitialiseerd")
            return True

        except Exception as e:
            logger.error(f"Fout tijdens initialisatie: {e}", exc_info=True)
            return False

    async def start(self):
        """Start de trading bot."""
        if self.running:
            logger.warning("Bot is al gestart")
            return

        try:
            logger.info("Start Trading Bot...")
            self.running = True

            # Start de Telegram bot
            if self.telegram_app:
                await self.telegram_app.initialize()
                await self.telegram_app.start()
                logger.info("Telegram bot is gestart")

                # Houd de bot actief
                await self.telegram_app.updater.start_polling()
                logger.info("Bot is actief en luistert naar commando's")

                # Houd de main thread actief
                await self.telegram_app.updater.idle()

        except Exception as e:
            logger.error(f"Fout tijdens het starten van de bot: {e}", exc_info=True)
            self.running = False
            raise

    async def stop(self):
        """Stop de trading bot veilig."""
        if not self.running:
            return

        logger.info("Stop Trading Bot...")
        self.running = False

        try:
            # Stop de Telegram bot
            if self.telegram_app:
                await self.telegram_app.stop()
                logger.info("Telegram bot gestopt")

            # Stop de exchange manager
            if self.exchange_manager:
                await self.exchange_manager.close()
                logger.info("Exchange manager gestopt")

            # Sluit de database verbinding
            if self.database:
                await self.database.close()
                logger.info("Database verbinding gesloten")

        except Exception as e:
            logger.error(f"Fout tijdens het stoppen van de bot: {e}", exc_info=True)
        finally:
            logger.info("Trading Bot is gestopt")


async def main():
    """Hoofdfunctie om de trading bot te starten."""
    bot = TradingBot()

    try:
        # Initialiseer de bot
        if not await bot.initialize():
            logger.error("Initialisatie mislukt, kan bot niet starten")
            return

        # Start de bot
        await bot.start()

    except KeyboardInterrupt:
        logger.info("Ontvangen stop signaal (Ctrl+C)")
    except Exception as e:
        logger.critical(f"Onverwachte fout: {e}", exc_info=True)
    finally:
        # Zorg ervoor dat de bot netjes afsluit
        await bot.stop()


if __name__ == "__main__":
    asyncio.run(main())
