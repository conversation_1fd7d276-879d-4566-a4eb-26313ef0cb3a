"""
Exchange module voor de Prachtige Trading Bot.
Beheert de verbindingen met verschillende cryptocurrency exchanges.
"""

from typing import Dict, Any, Optional, List
import ccxt.async_support as ccxt
from config.config import KUCOIN_CONFIG, BINANCE_CONFIG

class ExchangeManager:
    """Beheert verbindingen met verschillende cryptocurrency exchanges."""

    def __init__(self):
        self.exchanges: Dict[str, ccxt.Exchange] = {}
        self.initialized = False

    async def initialize(self):
        """Initialiseer verbindingen met de exchanges."""
        if self.initialized:
            return

        try:
            # Initialiseer KuCoin Futures
            self.exchanges['kucoin'] = ccxt.kucoinfutures({
                **KUCOIN_CONFIG,
                'options': {
                    'defaultType': 'future',
                    'adjustForTimeDifference': True,
                }
            })

            # Initialiseer Binance Futures
            self.exchanges['binance'] = ccxt.binance({
                **BINANCE_CONFIG,
                'options': {
                    'defaultType': 'future',
                    'adjustForTimeDifference': True,
                }
            })

            # Test de verbindingen
            for name, exchange in self.exchanges.items():
                try:
                    await exchange.load_markets()
                    print(f"{name} verbonden: {exchange.name}")
                except Exception as e:
                    print(f"Fout bij het verbinden met {name}: {e}")
                    raise

            self.initialized = True
            return True

        except Exception as e:
            print(f"Fout tijdens initialisatie van exchanges: {e}")
            await self.close()
            raise

    def get_exchange(self, name: str) -> Optional[ccxt.Exchange]:
        """Haal een exchange op bij naam."""
        return self.exchanges.get(name.lower())

    async def get_balance(self, exchange_name: str = 'kucoin') -> Dict[str, Any]:
        """Haal balans op van een exchange."""
        if exchange_name not in self.exchanges:
            return {}

        try:
            exchange = self.exchanges[exchange_name]
            balance = await exchange.fetch_balance()
            return balance
        except Exception as e:
            print(f"❌ Fout bij ophalen balans van {exchange_name}: {e}")
            return {}

    async def get_ticker(self, symbol: str, exchange_name: str = 'kucoin') -> Dict[str, Any]:
        """Haal ticker informatie op."""
        if exchange_name not in self.exchanges:
            return {}

        try:
            exchange = self.exchanges[exchange_name]
            ticker = await exchange.fetch_ticker(symbol)
            return ticker
        except Exception as e:
            print(f"❌ Fout bij ophalen ticker {symbol} van {exchange_name}: {e}")
            return {}

    async def get_ohlcv(self, symbol: str, timeframe: str = '1h', limit: int = 100,
                       exchange_name: str = 'kucoin') -> List[List]:
        """Haal OHLCV data op."""
        if exchange_name not in self.exchanges:
            return []

        try:
            exchange = self.exchanges[exchange_name]
            ohlcv = await exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            return ohlcv
        except Exception as e:
            print(f"❌ Fout bij ophalen OHLCV {symbol} van {exchange_name}: {e}")
            return []

    def get_available_exchanges(self) -> List[str]:
        """Krijg lijst van beschikbare exchanges."""
        return list(self.exchanges.keys())

    async def close(self):
        """Sluit alle exchange verbindingen."""
        for name, exchange in self.exchanges.items():
            try:
                if hasattr(exchange, 'close') and callable(exchange.close):
                    await exchange.close()
                print(f"✅ {name} verbinding gesloten")
            except Exception as e:
                print(f"⚠️ Fout bij sluiten {name}: {e}")

        self.exchanges = {}
        self.initialized = False
        print("✅ Exchange Manager gesloten")
