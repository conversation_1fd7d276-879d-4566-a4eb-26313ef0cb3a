# 💬 PRACHTIGE COMMUNITY BOT SETUP

## 🚀 Overzicht

De Prachtige Community Bot is een aparte Telegram bot die speciaal ontworpen is voor sociale trading functies. Gebruikers kunnen hier:

- 📸 Screenshots van trades delen
- 💰 Trading resultaten posten
- 🏆 Deelnemen aan weekly leaderboards
- 💬 <PERSON><PERSON> met andere traders
- 🎯 Achievements verdienen

## 📋 Setup Instructies

### 1. **Nieuwe Telegram Bot Aanmaken**

1. Ga naar [@BotFather](https://t.me/BotFather) op Telegram
2. Type `/newbot`
3. Kies een naam: `Prachtige Community Bot`
4. Kies een username: `PrachtigeCommunityBot` (moet eindigen op 'bot')
5. Kopieer de bot token die je krijgt

### 2. **Bot Token Configureren**

Voeg de community bot token toe aan je `.env` bestand:

```env
COMMUNITY_BOT_TOKEN=jouw_community_bot_token_hier
```

### 3. **Community Bot Starten**

```bash
# Start de community bot (aparte terminal)
python community_bot.py
```

### 4. **Bot Configuratie**

In BotFather, configureer je community bot:

```
/setdescription - Stel een beschrijving in
/setabouttext - Voeg about tekst toe
/setuserpic - Upload een profielfoto
/setcommands - Configureer commando's
```

**Aanbevolen commando's:**
```
start - Start de community bot
help - Toon help informatie
stats - Bekijk je statistieken
leaderboard - Weekly profit leaderboard
```

## 🎯 Features

### 📸 **Photo Sharing**
- Gebruikers kunnen trade screenshots uploaden
- Automatische post tracking
- Like en comment systeem

### 💰 **Profit Tracking**
- Automatische detectie van profit berichten
- Ondersteuning voor €, $ en % formaten
- Weekly leaderboard systeem

### 🏆 **Achievement System**
- Eerste foto gedeeld
- Eerste profit gedeeld
- Actieve trader (10+ posts)
- High profit (€1000+)

### 📊 **Weekly Leaderboard**
- Automatische ranking op basis van profits
- Top 10 performers
- Wekelijkse reset

## 🔗 Integratie met Main Bot

De hoofdbot heeft een "💬 Community" knop die gebruikers naar de community bot stuurt. De link wordt automatisch gegenereerd naar:

`https://t.me/PrachtigeCommunityBot`

## 📁 Data Opslag

De community bot slaat data op in:
- `data/community_data.json` - Gebruikersdata, posts, achievements
- Automatische backup bij elke wijziging

## 🚀 Gebruik

### Voor Gebruikers:

1. **Start de bot:** `/start`
2. **Deel screenshot:** Stuur een foto met caption
3. **Post profit:** Type "Profit: €500 op BTC/USDT"
4. **Bekijk stats:** `/stats` of klik op "📊 Mijn Stats"
5. **Leaderboard:** `/leaderboard` of klik op "🏆 Leaderboard"

### Profit Formaten:
- `Profit: €500 op BTC/USDT`
- `Winst: 15% deze week`
- `Gewonnen €1200 met ETH trade`
- `$300 profit vandaag`

## 🔧 Technische Details

### Dependencies:
- `python-telegram-bot==21.0`
- `python-dotenv==1.0.0`

### Bestandsstructuur:
```
community_bot.py          # Hoofdbestand
data/
  └── community_data.json  # Data opslag
```

### Logging:
- Alle activiteiten worden gelogd
- Error handling voor stabiliteit
- Automatische data backup

## 🎨 Customization

Je kunt de bot aanpassen door:

1. **Achievement criteria** wijzigen in `check_achievements()`
2. **Profit detectie** verbeteren in `handle_profit_sharing()`
3. **Nieuwe features** toevoegen in `callback_handler()`

## 🔒 Security

- Geen gevoelige data opslag
- Alleen publieke trading informatie
- Automatische data validatie

## 📞 Support

Voor vragen over de community bot setup:
- Check de logs voor error berichten
- Zorg dat beide bots verschillende tokens hebben
- Test eerst in een private chat

## 🎉 Klaar!

Na setup kunnen gebruikers:
1. De hoofdbot gebruiken voor trading
2. Op "💬 Community" klikken
3. Doorgestuurd worden naar de community bot
4. Beginnen met delen van trades en resultaten!

**Veel succes met je trading community!** 🚀
