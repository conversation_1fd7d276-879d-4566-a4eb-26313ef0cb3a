#!/usr/bin/env python3
"""
🧪 FINAL BOT TEST - Test volledige bot functionaliteit
===================================================

Finale test om te zien of de bot volledig kan opstarten en werken.
"""

import sys
import os
import asyncio
import logging
from pathlib import Path

# Voeg project root toe aan path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Setup basic logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_database_operations():
    """Test database operaties"""
    try:
        logger.info("🗄️ Testing database operations...")

        from database.database_manager import DatabaseManager
        db = DatabaseManager()
        await db.initialize()

        # Test signal storage
        signal_data = {
            'symbol': 'BTC/USDT',
            'strategy': 'test_strategy',
            'recommendation': 'buy',
            'confidence': 0.8,
            'indicators': {'rsi': 30, 'macd': 0.5}
        }

        success = await db.store_signal(signal_data)
        if success:
            logger.info("✅ Signal storage test passed")
        else:
            logger.error("❌ Signal storage test failed")
            return False

        # Test trade storage
        trade_data = {
            'user_id': 123456,
            'symbol': 'BTC/USDT',
            'side': 'buy',
            'amount': 0.001,
            'price': 44000.0,
            'pnl': 25.0,
            'status': 'closed'
        }

        success = await db.store_trade(trade_data)
        if success:
            logger.info("✅ Trade storage test passed")
        else:
            logger.error("❌ Trade storage test failed")
            return False

        # Test user stats
        stats = await db.get_user_stats(123456)
        logger.info(f"✅ User stats: {stats}")

        await db.close()
        return True

    except Exception as e:
        logger.error(f"❌ Database operations test failed: {e}")
        return False

async def test_exchange_manager():
    """Test exchange manager"""
    try:
        logger.info("💱 Testing exchange manager...")

        from trading_bot.exchanges import ExchangeManager
        exchange_manager = ExchangeManager()

        # Test initialization (should work even without API keys)
        await exchange_manager.initialize()

        # Test available exchanges
        exchanges = exchange_manager.get_available_exchanges()
        logger.info(f"✅ Available exchanges: {exchanges}")

        await exchange_manager.close()
        return True

    except Exception as e:
        logger.error(f"❌ Exchange manager test failed: {e}")
        return False

async def test_strategy_manager():
    """Test strategy manager"""
    try:
        logger.info("📊 Testing strategy manager...")

        from trading_bot.strategies import StrategyManager
        strategy_manager = StrategyManager()

        # Test initialization
        await strategy_manager.initialize()

        # Test strategy registration
        from trading_bot.strategies import MovingAverageStrategy
        ma_strategy = MovingAverageStrategy({'symbol': 'BTC/USDT', 'timeframe': '1h'})
        await strategy_manager.add_strategy(ma_strategy)

        logger.info(f"✅ Strategies registered: {list(strategy_manager.strategies.keys())}")

        await strategy_manager.stop()
        return True

    except Exception as e:
        logger.error(f"❌ Strategy manager test failed: {e}")
        return False

async def test_telegram_components():
    """Test Telegram componenten"""
    try:
        logger.info("📱 Testing Telegram components...")

        # Test keyboards
        from bot_telegram.telegram.keyboards import (
            get_main_keyboard, get_trading_keyboard,
            get_portfolio_keyboard, get_admin_keyboard
        )

        keyboards = {
            'main': get_main_keyboard(),
            'trading': get_trading_keyboard(),
            'portfolio': get_portfolio_keyboard(),
            'admin': get_admin_keyboard()
        }

        for name, keyboard in keyboards.items():
            logger.info(f"✅ {name} keyboard: {len(keyboard.inline_keyboard)} rows")

        # Test handlers import
        from bot_telegram.telegram.handlers import (
            start_handler, help_handler, trading_handler, admin_handler
        )
        logger.info("✅ All handlers imported successfully")

        return True

    except Exception as e:
        logger.error(f"❌ Telegram components test failed: {e}")
        return False

async def main():
    """Voer alle finale tests uit"""
    logger.info("🧪 FINAL BOT TEST FOR PRACHTIGE TRADING BOT")
    logger.info("=" * 60)

    tests = [
        ("Database Operations", test_database_operations),
        ("Exchange Manager", test_exchange_manager),
        ("Strategy Manager", test_strategy_manager),
        ("Telegram Components", test_telegram_components),
    ]

    results = {}

    for test_name, test_func in tests:
        logger.info(f"\n📋 TEST: {test_name}")
        try:
            result = await test_func()
            results[test_name] = result
            status = "✅ PASS" if result else "❌ FAIL"
            logger.info(f"  Result: {status}")
        except Exception as e:
            logger.error(f"  ❌ EXCEPTION: {e}")
            results[test_name] = False

    # Resultaten samenvatting
    logger.info("\n" + "=" * 60)
    logger.info("📊 FINAL TEST RESULTS:")

    passed = 0
    total = len(tests)

    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"  {test_name}: {status}")
        if result:
            passed += 1

    logger.info(f"\n🎯 OVERALL: {passed}/{total} tests passed")

    if passed == total:
        logger.info("\n🎉 ALL TESTS PASSED!")
        logger.info("🚀 Bot is ready for production!")
        logger.info("💡 You can now run: python main.py")
        return True
    else:
        logger.info(f"\n⚠️ {total - passed} tests failed.")
        logger.info("🔧 Please check the logs above for details.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
