# Core Dependencies
python-telegram-bot==21.0  # Latest stable version
python-dotenv==1.0.0
aiofiles==23.2.1
schedule==1.2.1
pytz==2024.1
tzlocal==4.3.1  # Version compatible with APScheduler 3.x

# Data & Analysis
ccxt>=4.4.78
pandas>=2.0.3
numpy>=1.24.3
ta-lib==0.4.28  # Technical analysis library
python-binance==1.0.19  # For Binance API
kucoin-futures-python==1.0.9  # For KuCoin Futures API

# Machine Learning (optional)
scikit-learn>=1.3.0
tensorflow>=2.13.0

# Utils
python-dateutil==2.8.2
pytimeparse==1.1.8
requests==2.31.0
websockets==11.0.3

# Logging
loguru==0.7.2

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
