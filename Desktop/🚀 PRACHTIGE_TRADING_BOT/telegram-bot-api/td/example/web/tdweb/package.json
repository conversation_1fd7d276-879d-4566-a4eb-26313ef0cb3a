{"name": "tdweb", "version": "1.8.47", "description": "JavaScript interface for TDLib (Telegram library)", "main": "dist/tdweb.js", "repository": {"type": "git", "url": "https://github.com/tdlib/td.git", "directory": "example/web/tdweb"}, "files": ["dist"], "scripts": {"build": "webpack --mode production", "start": "webpack-dev-server --open"}, "keywords": ["telegram"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "devDependencies": {"@babel/core": "^7.4.3", "@babel/plugin-syntax-dynamic-import": "^7.2.0", "@babel/plugin-transform-runtime": "^7.4.3", "@babel/preset-env": "^7.4.3", "@typescript-eslint/eslint-plugin": "^1.7.0", "acorn": "^6.4.1", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.5", "clean-webpack-plugin": "^2.0.1", "eslint": "^5.16.0", "eslint-config-react-app": "^4.0.0", "eslint-loader": "^2.1.2", "eslint-plugin-flowtype": "^2.0.0", "eslint-plugin-import": "^2.17.2", "eslint-plugin-jsx-a11y": "^6.2.1", "eslint-plugin-react": "^7.12.4", "eslint-plugin-react-hooks": "^1.6.0", "file-loader": "^3.0.1", "husky": "^1.3.1", "lint-staged": "^8.1.5", "prettier": "^1.17.0", "typescript": "^3.4.5", "webpack": "^4.44.1", "webpack-cli": "^3.3.12", "worker-loader": "^2.0.0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"linters": {"webpack.config.json": ["prettier --single-quote --write", "git add"], "package.json": ["prettier --single-quote --write", "git add"], "src/*.{js,jsx,json,css}": ["prettier --single-quote --write", "git add"]}}, "dependencies": {"@babel/runtime": "^7.4.3", "broadcast-channel": "^2.1.12", "localforage": "^1.7.3", "uuid": "^3.3.2"}, "babel": {"presets": ["@babel/env"], "plugins": ["@babel/syntax-dynamic-import", "@babel/transform-runtime"]}, "eslintConfig": {"extends": "eslint-config-react-app", "env": {"worker": true, "node": true, "browser": true}, "globals": {"WebAssembly": true}, "settings": {"react": {"version": "999.999.999"}}}}