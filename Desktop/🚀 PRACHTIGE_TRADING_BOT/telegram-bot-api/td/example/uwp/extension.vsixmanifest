<PackageManifest Version="2.0.0" xmlns="http://schemas.microsoft.com/developer/vsx-schema/2011">
  <Metadata>
    <Identity Id="Telegram.Td.UWP" Version="1.8.47" Language="en-US" Publisher="Telegram LLC" />
    <DisplayName>TDLib for Universal Windows Platform</DisplayName>
    <Description>TDLib is a library for building Telegram clients</Description>
    <MoreInfo>https://core.telegram.org/tdlib</MoreInfo>
    <Tags>Telegram, TDLib, library, client, API</Tags>
    <License>LICENSE_1_0.txt</License>
    <ReleaseNotes>https://github.com/tdlib/td/blob/master/CHANGELOG.md</ReleaseNotes>
  </Metadata>
  <Installation Scope="Global">
    <InstallationTarget Id="Microsoft.ExtensionSDK" TargetPlatformIdentifier="UAP" TargetPlatformVersion="v0.8.0.0" SdkName="Telegram.Td.UWP" SdkVersion="1.0" />
  </Installation>
  <Assets>
    <Asset Type="Microsoft.ExtensionSDK" Path="SDKManifest.xml" />
  </Assets>
</PackageManifest>
