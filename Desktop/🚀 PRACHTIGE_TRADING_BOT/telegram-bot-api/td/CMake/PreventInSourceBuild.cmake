function(prevent_in_source_build)
  get_filename_component(REAL_SOURCE_DIR "${CMAKE_CURRENT_SOURCE_DIR}" REALPATH)
  get_filename_component(REAL_BINARY_DIR "${CMAKE_CURRENT_BINARY_DIR}" REALPATH)

  if (REAL_BINARY_DIR STREQUAL REAL_SOURCE_DIR)
    message("  Out-of-source build must be used. Remove the files already")
    message("  created by <PERSON><PERSON><PERSON> and rerun CMake from a new directory:")
    message("    rm -rf CMakeFiles CMakeCache.txt")
    message("    mkdir build")
    message("    cd build")
    message("    cmake ..")
    message(FATAL_ERROR "In-source build failed.")
  endif()
endfunction()
