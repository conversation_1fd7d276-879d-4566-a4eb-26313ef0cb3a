//
// Copyright <PERSON><PERSON><PERSON> (<EMAIL>), <PERSON><PERSON><PERSON> (<EMAIL>) 2014-2025
//
// Distributed under the Boost Software License, Version 1.0. (See accompanying
// file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
//
#include "td/utils/FlatHashMap.h"

#ifdef SCOPE_EXIT
#undef SCOPE_EXIT
#endif

#include <absl/container/flat_hash_map.h>
#include <array>
#include <folly/container/F14Map.h>
#include <map>
#include <unordered_map>

#define test_map td::FlatHashMap
//#define test_map folly::F14FastMap
//#define test_map absl::flat_hash_map
//#define test_map std::map
//#define test_map std::unordered_map

//#define CREATE_MAP(num) CREATE_MAP_IMPL(num)
#define CREATE_MAP(num)

#define CREATE_MAP_IMPL(num)                      \
  int f_##num() {                                 \
    test_map<td::int32, std::array<char, num>> m; \
    m.emplace(1, std::array<char, num>{});        \
    int sum = 0;                                  \
    for (auto &it : m) {                          \
      sum += it.first;                            \
    }                                             \
    auto it = m.find(1);                          \
    sum += it->first;                             \
    m.erase(it);                                  \
    return sum;                                   \
  }                                               \
  int x_##num = f_##num()

CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);
CREATE_MAP(__LINE__);

int main() {
}
