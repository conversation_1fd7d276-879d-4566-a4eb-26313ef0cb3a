"""
🗄️ DATABASE MANAGER - Simple File-based Database
===============================================

Simple database implementation using JSON files for quick setup.
Can be easily replaced with SQLite or PostgreSQL later.
"""

import json
import asyncio
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
import aiofiles

logger = logging.getLogger(__name__)

class DatabaseManager:
    """
    📁 Simple File-based Database Manager

    Uses JSON files for quick setup and testing.
    Perfect for getting the bot running quickly!
    """

    def __init__(self, data_dir: str = "database"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)

        # Database files
        self.users_file = self.data_dir / "users.json"
        self.sessions_file = self.data_dir / "sessions.json"
        self.trades_file = self.data_dir / "trades.json"
        self.signals_file = self.data_dir / "signals.json"
        self.api_keys_file = self.data_dir / "api_keys.json"

        # In-memory cache
        self._users_cache = {}
        self._load_cache()

        logger.info("🗄️ Database Manager initialized (File-based)")

    def _load_cache(self):
        """Load data into memory cache"""
        try:
            if self.users_file.exists():
                with open(self.users_file, 'r') as f:
                    self._users_cache = json.load(f)
        except Exception as e:
            logger.warning(f"⚠️ Could not load users cache: {e}")
            self._users_cache = {}

    async def _save_users(self):
        """Save users cache to file"""
        try:
            with open(self.users_file, 'w') as f:
                json.dump(self._users_cache, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"❌ Error saving users: {e}")

    async def initialize(self):
        """Initialize database"""
        logger.info("🚀 Database initialized successfully")

    async def close(self):
        """Close database connections"""
        await self._save_users()
        logger.info("✅ Database closed")

    # =================================================================================
    # 👥 USER OPERATIONS
    # =================================================================================

    async def create_user(self, user_data: Dict[str, Any]) -> bool:
        """Create a new user"""
        try:
            user_id = str(user_data['user_id'])
            user_data['created_at'] = datetime.now().isoformat()
            user_data['last_activity'] = datetime.now().isoformat()

            self._users_cache[user_id] = user_data
            await self._save_users()

            logger.info(f"👤 User created: {user_id}")
            return True

        except Exception as e:
            logger.error(f"❌ Error creating user: {e}")
            return False

    async def get_user(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user by ID"""
        try:
            user_data = self._users_cache.get(str(user_id))
            if user_data:
                # Convert ISO strings back to datetime for certain fields
                if isinstance(user_data.get('created_at'), str):
                    user_data['created_at'] = datetime.fromisoformat(user_data['created_at'])
                if isinstance(user_data.get('last_activity'), str):
                    user_data['last_activity'] = datetime.fromisoformat(user_data['last_activity'])
            return user_data
        except Exception as e:
            logger.error(f"❌ Error getting user {user_id}: {e}")
            return None

    async def update_user(self, user_id: int, updates: Dict[str, Any]) -> bool:
        """Update user information"""
        try:
            user_id_str = str(user_id)
            if user_id_str in self._users_cache:
                # Convert datetime objects to ISO strings for storage
                for key, value in updates.items():
                    if isinstance(value, datetime):
                        updates[key] = value.isoformat()

                self._users_cache[user_id_str].update(updates)
                await self._save_users()
                return True
            return False

        except Exception as e:
            logger.error(f"❌ Error updating user {user_id}: {e}")
            return False

    async def get_all_users(self) -> List[Dict[str, Any]]:
        """Get all users"""
        try:
            users = []
            for user_data in self._users_cache.values():
                # Convert ISO strings to datetime
                user_copy = user_data.copy()
                if isinstance(user_copy.get('created_at'), str):
                    user_copy['created_at'] = datetime.fromisoformat(user_copy['created_at'])
                if isinstance(user_copy.get('last_activity'), str):
                    user_copy['last_activity'] = datetime.fromisoformat(user_copy['last_activity'])
                users.append(user_copy)
            return users
        except Exception as e:
            logger.error(f"❌ Error getting all users: {e}")
            return []

    # =================================================================================
    # 📊 USER STATISTICS
    # =================================================================================

    async def count_users(self) -> int:
        """Count total users"""
        return len(self._users_cache)

    async def count_active_users(self, since: datetime) -> int:
        """Count active users since given time"""
        count = 0
        for user_data in self._users_cache.values():
            last_activity = user_data.get('last_activity')
            if isinstance(last_activity, str):
                last_activity = datetime.fromisoformat(last_activity)
            if last_activity and last_activity > since:
                count += 1
        return count

    async def count_premium_users(self) -> int:
        """Count premium users"""
        count = 0
        for user_data in self._users_cache.values():
            if user_data.get('role') in ['premium', 'vip', 'admin']:
                count += 1
        return count

    async def count_vip_users(self) -> int:
        """Count VIP users"""
        count = 0
        for user_data in self._users_cache.values():
            if user_data.get('role') in ['vip', 'admin']:
                count += 1
        return count

    # =================================================================================
    # 🔑 API KEYS OPERATIONS
    # =================================================================================

    async def store_user_api_keys(self, user_id: int, exchange: str, api_data: Dict[str, str]) -> bool:
        """Store user API keys"""
        try:
            # Load existing API keys
            api_keys = {}
            if self.api_keys_file.exists():
                with open(self.api_keys_file, 'r') as f:
                    api_keys = json.load(f)

            # Store keys
            user_key = f"{user_id}_{exchange}"
            api_keys[user_key] = {
                'user_id': user_id,
                'exchange': exchange,
                'data': api_data,
                'created_at': datetime.now().isoformat()
            }

            # Save to file
            with open(self.api_keys_file, 'w') as f:
                json.dump(api_keys, f, indent=2)

            logger.info(f"🔑 API keys stored for user {user_id} ({exchange})")
            return True

        except Exception as e:
            logger.error(f"❌ Error storing API keys: {e}")
            return False

    async def get_user_api_keys(self, user_id: int, exchange: str) -> Optional[Dict[str, str]]:
        """Get user API keys"""
        try:
            if not self.api_keys_file.exists():
                return None

            with open(self.api_keys_file, 'r') as f:
                api_keys = json.load(f)

            user_key = f"{user_id}_{exchange}"
            if user_key in api_keys:
                return api_keys[user_key]['data']

            return None

        except Exception as e:
            logger.error(f"❌ Error getting API keys: {e}")
            return None

    # =================================================================================
    # 🔧 UTILITY METHODS
    # =================================================================================

    async def test_connection(self) -> bool:
        """Test database connection"""
        try:
            # Test file operations
            test_file = self.data_dir / "test.json"
            with open(test_file, 'w') as f:
                json.dump({"test": True}, f)
            test_file.unlink()  # Delete test file
            return True
        except Exception as e:
            logger.error(f"❌ Database test failed: {e}")
            return False

    async def backup_data(self) -> str:
        """Create backup of all data"""
        try:
            backup_dir = Path("backups")
            backup_dir.mkdir(exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = backup_dir / f"backup_{timestamp}.json"

            backup_data = {
                'users': self._users_cache,
                'created_at': datetime.now().isoformat(),
                'version': '2.0.0'
            }

            with open(backup_file, 'w') as f:
                json.dump(backup_data, f, indent=2, default=str)

            logger.info(f"💾 Backup created: {backup_file}")
            return str(backup_file)

        except Exception as e:
            logger.error(f"❌ Backup failed: {e}")
            return ""

    # =================================================================================
    # 📊 SIGNALS OPERATIONS
    # =================================================================================

    async def store_signal(self, signal_data: Dict[str, Any]) -> bool:
        """Store a trading signal"""
        try:
            # Load existing signals
            signals = []
            if self.signals_file.exists():
                with open(self.signals_file, 'r') as f:
                    signals = json.load(f)

            # Add new signal
            signal_data['id'] = len(signals) + 1
            signal_data['created_at'] = datetime.now().isoformat()
            signals.append(signal_data)

            # Keep only last 1000 signals
            if len(signals) > 1000:
                signals = signals[-1000:]

            # Save to file
            with open(self.signals_file, 'w') as f:
                json.dump(signals, f, indent=2, default=str)

            logger.info(f"📊 Signal stored: {signal_data.get('symbol')} - {signal_data.get('recommendation')}")
            return True

        except Exception as e:
            logger.error(f"❌ Error storing signal: {e}")
            return False

    async def get_recent_signals(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent signals"""
        try:
            if not self.signals_file.exists():
                return []

            with open(self.signals_file, 'r') as f:
                signals = json.load(f)

            # Return most recent signals
            return signals[-limit:] if len(signals) > limit else signals

        except Exception as e:
            logger.error(f"❌ Error getting signals: {e}")
            return []

    # =================================================================================
    # 💹 TRADES OPERATIONS
    # =================================================================================

    async def store_trade(self, trade_data: Dict[str, Any]) -> bool:
        """Store a trade"""
        try:
            # Load existing trades
            trades = []
            if self.trades_file.exists():
                with open(self.trades_file, 'r') as f:
                    trades = json.load(f)

            # Add new trade
            trade_data['id'] = len(trades) + 1
            trade_data['created_at'] = datetime.now().isoformat()
            trades.append(trade_data)

            # Keep only last 1000 trades
            if len(trades) > 1000:
                trades = trades[-1000:]

            # Save to file
            with open(self.trades_file, 'w') as f:
                json.dump(trades, f, indent=2, default=str)

            logger.info(f"💹 Trade stored: {trade_data.get('symbol')} - {trade_data.get('side')}")
            return True

        except Exception as e:
            logger.error(f"❌ Error storing trade: {e}")
            return False

    async def get_user_recent_trades(self, user_id: int, limit: int = 10) -> List[Dict]:
        """Get recent trades for a user"""
        try:
            if not self.trades_file.exists():
                return []

            with open(self.trades_file, 'r') as f:
                trades = json.load(f)

            # Filter trades for this user
            user_trades = [trade for trade in trades if trade.get('user_id') == user_id]

            # Return most recent trades
            return user_trades[-limit:] if len(user_trades) > limit else user_trades

        except Exception as e:
            logger.error(f"❌ Error getting user trades: {e}")
            return []

    async def get_user_stats(self, user_id: int) -> Dict[str, Any]:
        """Get user trading stats"""
        try:
            trades = await self.get_user_recent_trades(user_id, 100)  # Last 100 trades

            if not trades:
                return {
                    'balance': 0.0,
                    'open_positions': 0,
                    'daily_pnl': 0.0,
                    'success_rate': 0.0,
                    'total_trades': 0
                }

            # Calculate stats
            total_pnl = sum(trade.get('pnl', 0) for trade in trades)
            profitable_trades = len([t for t in trades if t.get('pnl', 0) > 0])
            success_rate = profitable_trades / len(trades) if trades else 0

            # Calculate daily PnL (last 24 hours)
            yesterday = datetime.now() - timedelta(days=1)
            daily_trades = [
                t for t in trades
                if datetime.fromisoformat(t.get('created_at', '')) > yesterday
            ]
            daily_pnl = sum(trade.get('pnl', 0) for trade in daily_trades)

            return {
                'balance': total_pnl,
                'open_positions': len([t for t in trades if t.get('status') == 'open']),
                'daily_pnl': daily_pnl,
                'success_rate': success_rate,
                'total_trades': len(trades)
            }

        except Exception as e:
            logger.error(f"❌ Error getting user stats: {e}")
            return {
                'balance': 0.0,
                'open_positions': 0,
                'daily_pnl': 0.0,
                'success_rate': 0.0,
                'total_trades': 0
            }
