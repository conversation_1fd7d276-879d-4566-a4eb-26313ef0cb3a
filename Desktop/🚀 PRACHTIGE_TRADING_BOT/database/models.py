"""
🗄️ DATABASE MODELS - Data Models
===============================

Simple data models for the trading bot.
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional, List, Dict, Any

@dataclass
class User:
    user_id: int
    username: Optional[str]
    first_name: str
    last_name: Optional[str]
    role: str = "free"
    status: str = "active"
    created_at: datetime = None
    last_activity: Optional[datetime] = None
    language: str = "en"
    trading_enabled: bool = False
    api_keys_configured: bool = False

@dataclass
class UserSession:
    user_id: int
    token: str
    created_at: datetime
    expires_at: datetime
    last_activity: datetime

@dataclass
class UserStats:
    user_id: int
    total_trades: int = 0
    winning_trades: int = 0
    total_volume: float = 0.0
    total_pnl: float = 0.0
