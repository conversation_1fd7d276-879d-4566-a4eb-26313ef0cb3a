#!/usr/bin/env python3
"""
🧪 SIMPLE BOT TEST - Minimal bot startup test
===========================================

Test om te zien of de bot kan opstarten zonder complexe dependencies.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Setup basic logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_simple_telegram_bot():
    """Test een eenvoudige Telegram bot zonder complexe dependencies."""
    try:
        logger.info("🤖 Testing simple Telegram bot...")

        # Import Telegram modules
        from telegram.ext import Application, CommandHandler
        from telegram import Update
        from telegram.ext import ContextTypes

        # Get token
        token = os.getenv('TELEGRAM_BOT_TOKEN')
        if not token:
            logger.error("❌ TELEGRAM_BOT_TOKEN not found in .env")
            return False

        logger.info(f"✅ Token found: ***{token[-10:]}")

        # Create simple handlers
        async def start_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
            user = update.effective_user
            await update.message.reply_text(
                f"🚀 **PRACHTIGE TRADING BOT TEST** 🚀\n\n"
                f"👋 Hallo {user.first_name}!\n"
                f"🆔 User ID: {user.id}\n\n"
                f"✅ Bot werkt perfect!\n"
                f"🎯 Dit is een test van de bot startup.",
                parse_mode='Markdown'
            )

        async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
            await update.message.reply_text(
                "📚 **BOT HELP** 📚\n\n"
                "🔹 /start - Start de bot\n"
                "🔹 /help - Toon deze help\n"
                "🔹 /test - Test functie\n\n"
                "🚀 **Bot startup test succesvol!**",
                parse_mode='Markdown'
            )

        async def test_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
            await update.message.reply_text(
                "🧪 **TEST RESULTS** 🧪\n\n"
                "✅ Telegram connection: OK\n"
                "✅ Message handling: OK\n"
                "✅ Commands: OK\n"
                "✅ Markdown parsing: OK\n\n"
                "🎉 **Alle tests geslaagd!**",
                parse_mode='Markdown'
            )

        # Create application without job queue to avoid weak reference issue
        logger.info("📱 Creating Telegram application...")
        builder = Application.builder()
        builder.token(token)
        builder.job_queue(None)  # Disable job queue to avoid weak reference issue
        application = builder.build()

        # Add handlers
        application.add_handler(CommandHandler("start", start_command))
        application.add_handler(CommandHandler("help", help_command))
        application.add_handler(CommandHandler("test", test_command))

        logger.info("✅ Handlers added successfully")

        # Initialize and start
        logger.info("🚀 Starting bot...")
        await application.initialize()
        await application.start()

        logger.info("✅ Bot started successfully!")
        logger.info("📱 Bot is now online and ready to receive messages!")
        logger.info("🎯 Try sending /start to the bot in Telegram")

        # Start polling
        await application.updater.start_polling()

        # Keep running for 30 seconds for testing
        logger.info("⏰ Running for 30 seconds for testing...")
        await asyncio.sleep(30)

        # Stop the bot
        logger.info("🛑 Stopping bot...")
        await application.updater.stop()
        await application.stop()
        await application.shutdown()

        logger.info("✅ Bot stopped successfully!")
        return True

    except Exception as e:
        logger.error(f"❌ Simple bot test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    logger.info("🧪 SIMPLE TELEGRAM BOT STARTUP TEST")
    logger.info("=" * 50)

    # Check environment
    token = os.getenv('TELEGRAM_BOT_TOKEN')
    if not token:
        logger.error("❌ TELEGRAM_BOT_TOKEN not found in .env file")
        logger.error("💡 Please add your bot token to the .env file")
        return False

    # Run test
    success = await test_simple_telegram_bot()

    # Results
    logger.info("\n" + "=" * 50)
    if success:
        logger.info("🎉 SIMPLE BOT TEST PASSED!")
        logger.info("✅ The Telegram bot can start and connect successfully!")
        logger.info("💡 You can now proceed with the full bot startup")
    else:
        logger.info("❌ SIMPLE BOT TEST FAILED!")
        logger.info("🔧 Please check the error messages above")

    return success

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Test stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
