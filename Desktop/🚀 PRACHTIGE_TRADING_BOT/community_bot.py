"""
🚀 PRACHTIGE TRADING BOT - COMMUNITY CHAT BOT
==============================================

Een aparte Telegram bot voor community functies:
- Foto/screenshot sharing van trades
- Sociale interactie tussen traders
- Wekelijkse profit announcements
- Trading resultaten delen

Author: Innovars Lab
Version: 2.0.0
"""

import asyncio
import logging
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path
import json
from typing import Dict, List

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from dotenv import load_dotenv
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import (
    Application, CommandHandler, MessageHandler, CallbackQueryHandler,
    filters, ContextTypes
)
from telegram.constants import ParseMode

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class CommunityTradingBot:
    """
    🚀 Community Trading Bot voor sociale trading functies
    """

    def __init__(self):
        """Initialize the community bot"""
        self.logger = logging.getLogger(__name__)
        self.logger.info("🚀 Community Trading Bot wordt opgestart...")

        # Bot configuration
        self.community_token = os.getenv('COMMUNITY_BOT_TOKEN')
        if not self.community_token:
            self.logger.error("❌ COMMUNITY_BOT_TOKEN niet gevonden in .env")
            sys.exit(1)

        # Data storage (simple file-based for now)
        self.data_file = "data/community_data.json"
        self.ensure_data_directory()
        self.community_data = self.load_community_data()

        # Setup Telegram application
        self.application = None
        self._setup_telegram_bot()

        self.logger.info("✅ Community Bot geïnitialiseerd!")

    def ensure_data_directory(self):
        """Ensure data directory exists"""
        os.makedirs("data", exist_ok=True)

    def load_community_data(self) -> Dict:
        """Load community data from file"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.warning(f"⚠️ Could not load community data: {e}")

        return {
            "users": {},
            "weekly_profits": {},
            "trade_posts": [],
            "achievements": []
        }

    def save_community_data(self):
        """Save community data to file"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.community_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"❌ Could not save community data: {e}")

    def _setup_telegram_bot(self):
        """Setup the Telegram bot with handlers"""
        try:
            # Create application
            builder = Application.builder()
            builder.token(self.community_token)
            builder.job_queue(None)  # Disable job queue

            self.application = builder.build()

            # Add handlers
            self.application.add_handler(CommandHandler("start", self.start_handler))
            self.application.add_handler(CommandHandler("help", self.help_handler))
            self.application.add_handler(CommandHandler("stats", self.stats_handler))
            self.application.add_handler(CommandHandler("leaderboard", self.leaderboard_handler))

            # Message handlers
            self.application.add_handler(MessageHandler(filters.PHOTO, self.photo_handler))
            self.application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self.message_handler))

            # Callback handler
            self.application.add_handler(CallbackQueryHandler(self.callback_handler))

            # Error handler
            self.application.add_error_handler(self.error_handler)

            self.logger.info("📱 Community bot handlers geconfigureerd")

        except Exception as e:
            self.logger.error(f"❌ Fout bij setup community bot: {e}")
            sys.exit(1)

    async def start_handler(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command for community bot"""
        user = update.effective_user
        user_id = str(user.id)

        # Register user in community
        if user_id not in self.community_data["users"]:
            self.community_data["users"][user_id] = {
                "username": user.username or "Anonymous",
                "first_name": user.first_name,
                "joined_date": datetime.now().isoformat(),
                "total_posts": 0,
                "total_profits_shared": 0,
                "achievements": []
            }
            self.save_community_data()

        text = f"""
💬 **WELKOM IN DE PRACHTIGE TRADING COMMUNITY!** 💬

Hallo {user.first_name}! Welkom in onze exclusieve trading community.

🌟 **WAT JE HIER KUNT DOEN:**
├─ 📸 Deel screenshots van je trades
├─ 💰 Post je trading resultaten
├─ 🏆 Bekijk de weekly leaderboard
├─ 💬 Chat met andere succesvolle traders
├─ 🎯 Leer van de beste strategieën
└─ 🚀 Inspireer anderen met je succes

📊 **COMMUNITY STATS:**
👥 Actieve Traders: {len(self.community_data["users"])}
📈 Gedeelde Trades: {len(self.community_data["trade_posts"])}
🏆 Achievements: {len(self.community_data["achievements"])}

**Deel je eerste trade en word onderdeel van onze succesvolle community!**
        """

        keyboard = [
            [InlineKeyboardButton("📸 Deel Trade Screenshot", callback_data="share_trade")],
            [InlineKeyboardButton("💰 Post Profit Resultaat", callback_data="post_profit")],
            [InlineKeyboardButton("🏆 Leaderboard", callback_data="leaderboard")],
            [InlineKeyboardButton("📊 Mijn Stats", callback_data="my_stats")],
            [InlineKeyboardButton("💡 Trading Tips", callback_data="trading_tips")],
            [InlineKeyboardButton("🔙 Terug naar Main Bot", url="https://t.me/Innovars_trading_trading_bot?start")]
        ]

        await update.message.reply_text(
            text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode=ParseMode.MARKDOWN
        )

    async def help_handler(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command"""
        text = """
❓ **COMMUNITY BOT HELP** ❓

🔧 **COMMANDO'S:**
├─ /start - Start de community bot
├─ /help - Toon deze help
├─ /stats - Bekijk je statistieken
└─ /leaderboard - Weekly profit leaderboard

📸 **FOTO'S DELEN:**
• Stuur gewoon een foto met je trade screenshot
• Voeg een beschrijving toe met je resultaat
• Gebruik hashtags zoals #profit #bitcoin #trading

💰 **PROFIT DELEN:**
• Type: "Profit: €500 op BTC/USDT"
• Of: "Winst: 15% deze week"
• Bot detecteert automatisch je resultaten

🏆 **ACHIEVEMENTS:**
• Eerste trade gedeeld
• 10+ trades gepost
• €1000+ profit gedeeld
• Top 3 weekly leaderboard

**Veel succes met traden! 🚀**
        """

        await update.message.reply_text(text, parse_mode=ParseMode.MARKDOWN)

    async def photo_handler(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle photo uploads (trade screenshots)"""
        user = update.effective_user
        user_id = str(user.id)

        # Get photo and caption
        photo = update.message.photo[-1]  # Get highest resolution
        caption = update.message.caption or "Trade screenshot gedeeld!"

        # Store trade post
        trade_post = {
            "user_id": user_id,
            "username": user.username or "Anonymous",
            "first_name": user.first_name,
            "photo_id": photo.file_id,
            "caption": caption,
            "timestamp": datetime.now().isoformat(),
            "likes": 0,
            "comments": []
        }

        self.community_data["trade_posts"].append(trade_post)

        # Update user stats
        if user_id in self.community_data["users"]:
            self.community_data["users"][user_id]["total_posts"] += 1

        self.save_community_data()

        # Check for achievements
        await self.check_achievements(user_id, "photo_shared")

        # Response with engagement options
        keyboard = [
            [InlineKeyboardButton("👍 Like", callback_data=f"like_{len(self.community_data['trade_posts'])-1}")],
            [InlineKeyboardButton("💬 Comment", callback_data=f"comment_{len(self.community_data['trade_posts'])-1}")],
            [InlineKeyboardButton("🔄 Share", callback_data=f"share_{len(self.community_data['trade_posts'])-1}")]
        ]

        await update.message.reply_text(
            f"🎉 **Geweldig!** Je trade screenshot is gedeeld!\n\n"
            f"📸 **Post #{len(self.community_data['trade_posts'])}**\n"
            f"👤 Door: {user.first_name}\n"
            f"📝 Beschrijving: {caption}\n\n"
            f"**Andere traders kunnen nu reageren op je post!**",
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode=ParseMode.MARKDOWN
        )

    async def message_handler(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle text messages (profit sharing, chat)"""
        user = update.effective_user
        user_id = str(user.id)
        text = update.message.text.lower()

        # Detect profit sharing
        profit_keywords = ["profit", "winst", "gewonnen", "€", "$", "%"]
        if any(keyword in text for keyword in profit_keywords):
            await self.handle_profit_sharing(update, context)
        else:
            # Regular chat message
            await self.handle_chat_message(update, context)

    async def handle_profit_sharing(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle profit sharing messages"""
        user = update.effective_user
        user_id = str(user.id)
        text = update.message.text

        # Extract profit amount (simple regex)
        import re

        # Look for profit patterns
        profit_amount = 0
        profit_percentage = 0

        # Find euro amounts
        euro_match = re.search(r'€(\d+(?:,\d+)?)', text)
        if euro_match:
            profit_amount = float(euro_match.group(1).replace(',', '.'))

        # Find dollar amounts
        dollar_match = re.search(r'\$(\d+(?:,\d+)?)', text)
        if dollar_match:
            profit_amount = float(dollar_match.group(1).replace(',', '.'))

        # Find percentage
        percent_match = re.search(r'(\d+(?:,\d+)?)%', text)
        if percent_match:
            profit_percentage = float(percent_match.group(1).replace(',', '.'))

        # Store profit data
        profit_data = {
            "user_id": user_id,
            "username": user.username or "Anonymous",
            "first_name": user.first_name,
            "message": text,
            "profit_amount": profit_amount,
            "profit_percentage": profit_percentage,
            "timestamp": datetime.now().isoformat(),
            "week": datetime.now().isocalendar()[1]  # Week number
        }

        # Add to weekly profits
        week_key = f"{datetime.now().year}-W{datetime.now().isocalendar()[1]}"
        if week_key not in self.community_data["weekly_profits"]:
            self.community_data["weekly_profits"][week_key] = []

        self.community_data["weekly_profits"][week_key].append(profit_data)

        # Update user stats
        if user_id in self.community_data["users"]:
            self.community_data["users"][user_id]["total_profits_shared"] += 1

        self.save_community_data()

        # Check for achievements
        await self.check_achievements(user_id, "profit_shared", profit_amount)

        # Check if this qualifies for weekly announcement
        if profit_amount >= 1000 or profit_percentage >= 20:
            await self.broadcast_profit_achievement(user, profit_amount, profit_percentage)

        # Response
        response = f"""
🎉 **FANTASTISCH RESULTAAT!** 🎉

👤 **Trader:** {user.first_name}
💰 **Profit:** {f"€{profit_amount}" if profit_amount > 0 else ""} {f"{profit_percentage}%" if profit_percentage > 0 else ""}
📅 **Datum:** {datetime.now().strftime('%d-%m-%Y')}

🏆 **Je bent toegevoegd aan de weekly leaderboard!**

**Blijf zo doorgaan en inspireer andere traders!** 🚀
        """

        keyboard = [
            [InlineKeyboardButton("🏆 Bekijk Leaderboard", callback_data="leaderboard")],
            [InlineKeyboardButton("📊 Mijn Stats", callback_data="my_stats")]
        ]

        await update.message.reply_text(
            response,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode=ParseMode.MARKDOWN
        )

    async def handle_chat_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle regular chat messages"""
        user = update.effective_user

        # Simple responses to encourage engagement
        responses = [
            f"💬 Interessant punt, {user.first_name}! Andere traders, wat denken jullie?",
            f"🤔 Goede vraag! Heeft iemand ervaring hiermee?",
            f"📈 Bedankt voor het delen! Meer traders zouden dit moeten lezen.",
            f"🚀 Geweldige inzichten! Blijf actief in onze community.",
            f"💡 Nuttige informatie! Anderen kunnen hier veel van leren."
        ]

        import random
        response = random.choice(responses)

        keyboard = [
            [InlineKeyboardButton("📸 Deel Trade Screenshot", callback_data="share_trade")],
            [InlineKeyboardButton("💰 Post Profit", callback_data="post_profit")]
        ]

        await update.message.reply_text(
            response,
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    async def check_achievements(self, user_id: str, action: str, amount: float = 0):
        """Check and award achievements"""
        user_data = self.community_data["users"].get(user_id, {})
        achievements = user_data.get("achievements", [])

        new_achievements = []

        # First photo shared
        if action == "photo_shared" and "first_photo" not in achievements:
            new_achievements.append("first_photo")

        # First profit shared
        if action == "profit_shared" and "first_profit" not in achievements:
            new_achievements.append("first_profit")

        # 10 posts milestone
        if user_data.get("total_posts", 0) >= 10 and "active_trader" not in achievements:
            new_achievements.append("active_trader")

        # High profit achievement
        if amount >= 1000 and "high_profit" not in achievements:
            new_achievements.append("high_profit")

        # Update user achievements
        if new_achievements:
            user_data["achievements"].extend(new_achievements)
            self.community_data["users"][user_id] = user_data
            self.save_community_data()

    async def broadcast_profit_achievement(self, user, profit_amount: float, profit_percentage: float):
        """Broadcast significant profit achievements to all users"""
        achievement_text = f"""
🚨 **WEEKLY PROFIT ALERT!** 🚨

🏆 **{user.first_name}** heeft een fantastisch resultaat behaald!

💰 **Profit:** {f"€{profit_amount}" if profit_amount > 0 else ""} {f"{profit_percentage}%" if profit_percentage > 0 else ""}
📅 **Deze week**

🎉 **Gefeliciteerd met dit geweldige resultaat!**

**Wil jij ook zulke resultaten? Leer van de beste traders in onze community!**
        """

        # In a real implementation, you would broadcast to all community members
        # For now, we'll just log it
        self.logger.info(f"🚨 Broadcasting achievement: {user.first_name} - €{profit_amount} / {profit_percentage}%")

    async def stats_handler(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /stats command"""
        user = update.effective_user
        user_id = str(user.id)

        user_data = self.community_data["users"].get(user_id, {})

        text = f"""
📊 **JOUW COMMUNITY STATS** 📊

👤 **Profiel:**
├─ 📛 Naam: {user.first_name}
├─ 📅 Lid sinds: {user_data.get('joined_date', 'Onbekend')[:10]}
└─ 🏆 Achievements: {len(user_data.get('achievements', []))}

📈 **Activiteit:**
├─ 📸 Posts gedeeld: {user_data.get('total_posts', 0)}
├─ 💰 Profits gedeeld: {user_data.get('total_profits_shared', 0)}
└─ 💬 Community rank: Top trader

🏆 **Achievements:**
        """

        achievements_map = {
            "first_photo": "📸 Eerste Screenshot Gedeeld",
            "first_profit": "💰 Eerste Profit Gedeeld",
            "active_trader": "🚀 Actieve Trader (10+ posts)",
            "high_profit": "💎 High Profit (€1000+)"
        }

        user_achievements = user_data.get('achievements', [])
        if user_achievements:
            for achievement in user_achievements:
                text += f"✅ {achievements_map.get(achievement, achievement)}\n"
        else:
            text += "🎯 Deel je eerste trade om achievements te verdienen!\n"

        text += "\n**Blijf actief en verdien meer achievements!** 🚀"

        keyboard = [
            [InlineKeyboardButton("📸 Deel Screenshot", callback_data="share_trade")],
            [InlineKeyboardButton("🏆 Leaderboard", callback_data="leaderboard")]
        ]

        await update.message.reply_text(
            text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode=ParseMode.MARKDOWN
        )

    async def leaderboard_handler(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /leaderboard command"""
        current_week = f"{datetime.now().year}-W{datetime.now().isocalendar()[1]}"
        weekly_profits = self.community_data["weekly_profits"].get(current_week, [])

        # Sort by profit amount
        sorted_profits = sorted(weekly_profits, key=lambda x: x.get('profit_amount', 0), reverse=True)

        text = f"""
🏆 **WEEKLY LEADERBOARD** 🏆
📅 **Week {datetime.now().isocalendar()[1]} - {datetime.now().year}**

💰 **TOP PERFORMERS:**

        """

        if sorted_profits:
            for i, profit in enumerate(sorted_profits[:10], 1):
                medal = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else f"{i}."
                text += f"{medal} **{profit['first_name']}** - €{profit.get('profit_amount', 0)}\n"
        else:
            text += "🎯 Nog geen profits gedeeld deze week!\n\n**Wees de eerste en claim de #1 positie!**"

        text += f"""

📊 **COMMUNITY STATS:**
├─ 👥 Actieve traders: {len(self.community_data["users"])}
├─ 📈 Trades gedeeld: {len(self.community_data["trade_posts"])}
└─ 💰 Profits deze week: {len(weekly_profits)}

**Deel jouw resultaten en klim naar de top!** 🚀
        """

        keyboard = [
            [InlineKeyboardButton("💰 Deel Mijn Profit", callback_data="post_profit")],
            [InlineKeyboardButton("📊 Mijn Stats", callback_data="my_stats")]
        ]

        await update.message.reply_text(
            text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode=ParseMode.MARKDOWN
        )

    async def callback_handler(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle callback queries from inline keyboards"""
        query = update.callback_query
        await query.answer()

        data = query.data

        if data == "share_trade":
            await query.edit_message_text(
                "📸 **DEEL JE TRADE SCREENSHOT**\n\n"
                "Stuur een foto van je trading platform met:\n"
                "├─ 💰 Je profit/loss\n"
                "├─ 📊 Het trading pair\n"
                "├─ ⏰ Tijdstip van de trade\n"
                "└─ 📝 Optioneel: beschrijving\n\n"
                "**Tip:** Voeg een caption toe met je resultaat!",
                parse_mode=ParseMode.MARKDOWN
            )

        elif data == "post_profit":
            await query.edit_message_text(
                "💰 **DEEL JE PROFIT RESULTAAT**\n\n"
                "Type je bericht met je resultaat, bijvoorbeeld:\n"
                "• 'Profit: €500 op BTC/USDT'\n"
                "• 'Winst: 15% deze week'\n"
                "• 'Gewonnen €1200 met ETH trade'\n\n"
                "**De bot detecteert automatisch je profit!**",
                parse_mode=ParseMode.MARKDOWN
            )

        elif data == "leaderboard":
            await self.leaderboard_handler(update, context)

        elif data == "my_stats":
            await self.stats_handler(update, context)

        elif data == "trading_tips":
            tips_text = """
💡 **TRADING TIPS VAN DE COMMUNITY** 💡

🎯 **Top Strategieën:**
├─ 📊 Altijd stop-loss gebruiken
├─ 💰 Nooit meer dan 2% risico per trade
├─ 📈 Trends volgen, niet voorspellen
├─ ⏰ Geduld is de sleutel tot succes
└─ 📚 Blijf leren van andere traders

🚀 **Community Wijsheid:**
• "Plan je trade, trade je plan"
• "Emoties zijn de vijand van profit"
• "Kleine winsten tellen op"
• "Verliezen zijn lessen in vermomming"

**Deel jouw tips met de community!**
            """

            keyboard = [
                [InlineKeyboardButton("📸 Deel Screenshot", callback_data="share_trade")],
                [InlineKeyboardButton("🔙 Terug", callback_data="back_to_main")]
            ]

            await query.edit_message_text(
                tips_text,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode=ParseMode.MARKDOWN
            )

        elif data.startswith("like_"):
            post_id = int(data.split("_")[1])
            if post_id < len(self.community_data["trade_posts"]):
                self.community_data["trade_posts"][post_id]["likes"] += 1
                self.save_community_data()
                await query.edit_message_text(
                    f"👍 **Like toegevoegd!**\n\n"
                    f"Post heeft nu {self.community_data['trade_posts'][post_id]['likes']} likes!",
                    parse_mode=ParseMode.MARKDOWN
                )

    async def error_handler(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle errors"""
        self.logger.error(f"❌ Community bot error: {context.error}")

    async def start(self):
        """Start the community bot"""
        try:
            self.logger.info("🚀 Community bot wordt gestart...")

            # Start the Telegram bot
            await self.application.initialize()
            await self.application.start()
            await self.application.updater.start_polling()

            self.logger.info("✅ Community Trading Bot is online!")
            self.logger.info("💬 Community functies zijn actief!")

            # Keep the bot running
            try:
                await self.application.updater.idle()
            except AttributeError:
                # For newer versions
                import signal

                stop_event = asyncio.Event()

                def signal_handler(signum, frame):
                    stop_event.set()

                signal.signal(signal.SIGINT, signal_handler)
                signal.signal(signal.SIGTERM, signal_handler)

                await stop_event.wait()

        except Exception as e:
            self.logger.error(f"❌ Fout bij starten community bot: {e}")

    async def stop(self):
        """Stop the community bot"""
        try:
            self.logger.info("🛑 Community bot wordt gestopt...")

            if self.application:
                await self.application.stop()
                await self.application.shutdown()

            self.logger.info("✅ Community bot succesvol gestopt")

        except Exception as e:
            self.logger.error(f"❌ Fout bij stoppen community bot: {e}")


def main():
    """Main function to start the community bot"""
    print("""
╔═══════════════════════════════════════════════════════════════╗
║                                                               ║
║         💬 PRACHTIGE COMMUNITY BOT v2.0.0 💬                ║
║                                                               ║
║    ┌─ 📸 Photo Sharing                                        ║
║    ├─ 💰 Profit Tracking                                     ║
║    ├─ 🏆 Weekly Leaderboards                                 ║
║    ├─ 🎯 Achievement System                                  ║
║    ├─ 💬 Social Trading                                      ║
║    └─ 🚀 Community Engagement                                ║
║                                                               ║
║                Made with ❤️ by Innovars Lab                  ║
╚═══════════════════════════════════════════════════════════════╝
    """)

    # Create and start community bot
    community_bot = CommunityTradingBot()

    try:
        # Run the bot
        asyncio.run(community_bot.start())
    except KeyboardInterrupt:
        print("\n🛑 Community bot gestopt door gebruiker")
    except Exception as e:
        print(f"\n❌ Onverwachte fout: {e}")
    finally:
        print("👋 Tot ziens!")


if __name__ == "__main__":
    main()