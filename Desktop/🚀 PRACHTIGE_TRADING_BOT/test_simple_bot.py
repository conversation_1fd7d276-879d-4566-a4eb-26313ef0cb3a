#!/usr/bin/env python3
"""
🧪 SIMPLE BOT TEST - Test basis bot functionaliteit
=================================================

Eenvoudige test om te zien of de bot componenten werken.
"""

import sys
import os
import asyncio
import logging
from pathlib import Path

# Voeg project root toe aan path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Setup basic logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_components():
    """Test individuele componenten zonder Telegram Application."""
    try:
        logger.info("🧪 Testing individual components...")

        # Test 1: Config
        logger.info("📋 Testing config...")
        from config.config import TELEGRAM_BOT_TOKEN, ADMIN_USER_IDS
        logger.info(f"✅ Config loaded - Token: {'***' + TELEGRAM_BOT_TOKEN[-10:] if TELEGRAM_BOT_TOKEN else 'None'}")

        # Test 2: Database Manager
        logger.info("🗄️ Testing database manager...")
        from database.database_manager import DatabaseManager
        db_manager = DatabaseManager()
        await db_manager.initialize()
        logger.info("✅ Database manager initialized")

        # Test 3: User Manager
        logger.info("👤 Testing user manager...")
        from core.user_manager import UserManager
        user_manager = UserManager(db_manager)
        logger.info("✅ User manager initialized")

        # Test 4: Bot Manager
        logger.info("🤖 Testing bot manager...")
        from core.bot_manager import BotManager
        from utils.config import Config
        config = Config()
        bot_manager = BotManager(db_manager, user_manager, config)
        logger.info("✅ Bot manager initialized")

        # Test 5: Keyboards
        logger.info("⌨️ Testing keyboards...")
        from bot_telegram.telegram.keyboards import get_main_keyboard
        keyboard = get_main_keyboard()
        logger.info(f"✅ Keyboard created with {len(keyboard.inline_keyboard)} rows")

        # Test 6: Handlers (import only)
        logger.info("🎯 Testing handlers import...")
        from bot_telegram.telegram.handlers import start_handler
        logger.info("✅ Handlers imported successfully")

        # Test 7: Exchange Manager
        logger.info("💱 Testing exchange manager...")
        from trading_bot.exchanges import ExchangeManager
        exchange_manager = ExchangeManager()
        logger.info("✅ Exchange manager initialized")

        # Test 8: Strategy Manager
        logger.info("📊 Testing strategy manager...")
        from trading_bot.strategies import StrategyManager
        strategy_manager = StrategyManager()
        logger.info("✅ Strategy manager initialized")

        # Cleanup
        await db_manager.close()

        logger.info("🎉 All components test PASSED!")
        return True

    except Exception as e:
        logger.error(f"❌ Component test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_telegram_token():
    """Test of de Telegram token geldig is."""
    try:
        logger.info("📱 Testing Telegram token...")

        from config.config import TELEGRAM_BOT_TOKEN
        if not TELEGRAM_BOT_TOKEN:
            logger.error("❌ No Telegram token found")
            return False

        # Test token format
        if not TELEGRAM_BOT_TOKEN.count(':') == 1:
            logger.error("❌ Invalid token format")
            return False

        bot_id, token_part = TELEGRAM_BOT_TOKEN.split(':')
        if not bot_id.isdigit() or len(token_part) < 30:
            logger.error("❌ Invalid token structure")
            return False

        logger.info(f"✅ Token format is valid - Bot ID: {bot_id}")
        return True

    except Exception as e:
        logger.error(f"❌ Token test failed: {e}")
        return False

async def main():
    """Voer alle tests uit."""
    logger.info("🧪 SIMPLE BOT TEST FOR PRACHTIGE TRADING BOT")
    logger.info("=" * 50)

    # Test 1: Components
    logger.info("\n📋 TEST 1: Component Tests")
    components_success = await test_components()

    # Test 2: Telegram Token
    logger.info("\n📋 TEST 2: Telegram Token Test")
    token_success = await test_telegram_token()

    # Resultaten
    logger.info("\n" + "=" * 50)
    logger.info("📊 TEST RESULTS:")
    logger.info(f"  🧩 Components: {'✅ PASS' if components_success else '❌ FAIL'}")
    logger.info(f"  📱 Token: {'✅ PASS' if token_success else '❌ FAIL'}")

    if components_success and token_success:
        logger.info("\n🎉 ALL TESTS PASSED!")
        logger.info("💡 Bot components are ready!")
        logger.info("🚀 You can try running the bot with: python main.py")
        return True
    else:
        logger.info("\n⚠️ Some tests failed. Check the logs above.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
