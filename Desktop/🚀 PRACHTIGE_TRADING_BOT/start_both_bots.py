#!/usr/bin/env python3
"""
🚀 PRACHTIGE TRADING BOT - DUAL BOT LAUNCHER
==============================================

Start beide bots tegelijk:
- Main Trading Bot (trading functies)
- Community Bot (sociale functies)

Author: Innovars Lab
Version: 2.0.0
"""

import asyncio
import logging
import subprocess
import sys
import time
from pathlib import Path

# Setup logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def start_bot_process(script_name, bot_name):
    """Start een bot proces"""
    try:
        logger.info(f"🚀 Starting {bot_name}...")
        process = subprocess.Popen(
            [sys.executable, script_name],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        logger.info(f"✅ {bot_name} started with PID: {process.pid}")
        return process
    except Exception as e:
        logger.error(f"❌ Failed to start {bot_name}: {e}")
        return None

def main():
    """Start beide bots"""
    print("""
╔═══════════════════════════════════════════════════════════════╗
║                                                               ║
║         🚀 PRACHTIGE DUAL BOT LAUNCHER v2.0.0 🚀            ║
║                                                               ║
║    ┌─ 📱 Main Trading Bot                                     ║
║    └─ 💬 Community Chat Bot                                  ║
║                                                               ║
║                Made with ❤️ by Innovars Lab                  ║
╚═══════════════════════════════════════════════════════════════╝
    """)
    
    logger.info("🚀 Starting Prachtige Trading Bot System...")
    
    # Start main trading bot
    main_bot = start_bot_process("main.py", "Main Trading Bot")
    if not main_bot:
        logger.error("❌ Failed to start main bot. Exiting.")
        return
    
    # Wait a bit before starting community bot
    time.sleep(2)
    
    # Start community bot
    community_bot = start_bot_process("community_bot.py", "Community Bot")
    if not community_bot:
        logger.error("❌ Failed to start community bot. Stopping main bot.")
        main_bot.terminate()
        return
    
    logger.info("✅ Both bots are now running!")
    logger.info("📱 Main Bot: Trading, signals, portfolio management")
    logger.info("💬 Community Bot: Photo sharing, profit tracking, leaderboards")
    logger.info("")
    logger.info("🔗 Users can switch between bots using the menu buttons")
    logger.info("⚠️  Press Ctrl+C to stop both bots")
    
    try:
        # Monitor both processes
        while True:
            # Check if processes are still running
            main_running = main_bot.poll() is None
            community_running = community_bot.poll() is None
            
            if not main_running:
                logger.error("❌ Main bot stopped unexpectedly!")
                break
                
            if not community_running:
                logger.error("❌ Community bot stopped unexpectedly!")
                break
            
            time.sleep(5)  # Check every 5 seconds
            
    except KeyboardInterrupt:
        logger.info("🛑 Stopping both bots...")
        
        # Terminate both processes
        if main_bot.poll() is None:
            main_bot.terminate()
            logger.info("🛑 Main bot stopped")
            
        if community_bot.poll() is None:
            community_bot.terminate()
            logger.info("🛑 Community bot stopped")
        
        # Wait for processes to finish
        main_bot.wait()
        community_bot.wait()
        
        logger.info("✅ Both bots stopped successfully")
        print("👋 Tot ziens!")

if __name__ == "__main__":
    main()
