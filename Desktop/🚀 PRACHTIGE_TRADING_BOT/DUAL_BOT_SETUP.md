# 🚀 PRACHTIGE DUAL BOT SYSTEM

## 🎯 Overzicht

Je hebt nu **twee aparte Telegram bots** die samenwerken:

### 📱 **Main Trading Bot**
- **Token:** `**********************************************`
- **Username:** `@PrachtigeTradingBot`
- **Functies:** Trading, portfolio, signalen, AI assistant

### 💬 **Community Bot** 
- **Token:** `8032308171:AAGH6nJ9Dnv3gsC4GlsrMtvtzyxQlOFWvRs`
- **Username:** `@PrachtigeCommunity_bot`
- **Functies:** Photo sharing, profit tracking, leaderboards

## 🚀 Starten

### **Optie 1: Beide bots tegelijk**
```bash
python start_both_bots.py
```

### **Optie 2: Aparte terminals**
```bash
# Terminal 1 - Main Bot
python main.py

# Terminal 2 - Community Bot  
python community_bot.py
```

## 🔗 Hoe het werkt

### **Voor Gebruikers:**

1. **Start in Main Bot:** `/start` → <PERSON>ie nieuwe welkomstboodschap
2. **Klik "💬 Community"** → Automatisch doorgestuurd naar Community Bot
3. **In Community Bot:** Klik "🔙 Terug naar Main Bot" → Terug naar Main Bot

### **Automatische Navigatie:**
- Knoppen sturen gebruikers automatisch naar de juiste bot
- Geen handmatige links nodig
- Naadloze overgang tussen functies

## 📸 Community Bot Features

### **Photo Sharing:**
```
Gebruiker stuurt foto → Bot reageert met:
🎉 Geweldig! Je trade screenshot is gedeeld!
📸 Post #1
👤 Door: [Naam]
📝 Beschrijving: [Caption]

[👍 Like] [💬 Comment] [🔄 Share]
```

### **Profit Tracking:**
```
Gebruiker typt: "Profit: €500 op BTC/USDT"
Bot detecteert automatisch en reageert:

🎉 FANTASTISCH RESULTAAT! 🎉
👤 Trader: [Naam]
💰 Profit: €500
📅 Datum: 25-05-2025
🏆 Je bent toegevoegd aan de weekly leaderboard!
```

### **Weekly Leaderboard:**
```
🏆 WEEKLY LEADERBOARD 🏆
📅 Week 21 - 2025

💰 TOP PERFORMERS:
🥇 John - €1500
🥈 Sarah - €800  
🥉 Mike - €650

📊 COMMUNITY STATS:
├─ 👥 Actieve traders: 15
├─ 📈 Trades gedeeld: 47
└─ 💰 Profits deze week: 12
```

## 🎯 Achievement System

### **Automatische Achievements:**
- 📸 **Eerste Screenshot** - Eerste foto gedeeld
- 💰 **Eerste Profit** - Eerste profit gepost
- 🚀 **Actieve Trader** - 10+ posts gedeeld
- 💎 **High Profit** - €1000+ profit behaald

### **Profit Announcements:**
Bij profits ≥ €1000 of ≥ 20%:
```
🚨 WEEKLY PROFIT ALERT! 🚨
🏆 [Naam] heeft een fantastisch resultaat behaald!
💰 Profit: €1200 / 25%
📅 Deze week
🎉 Gefeliciteerd met dit geweldige resultaat!
```

## 📊 Data Opslag

### **Community Data:**
```json
{
  "users": {
    "user_id": {
      "username": "trader123",
      "first_name": "John",
      "joined_date": "2025-05-25",
      "total_posts": 5,
      "total_profits_shared": 3,
      "achievements": ["first_photo", "first_profit"]
    }
  },
  "weekly_profits": {
    "2025-W21": [
      {
        "user_id": "123",
        "profit_amount": 500,
        "profit_percentage": 15,
        "timestamp": "2025-05-25T10:30:00"
      }
    ]
  },
  "trade_posts": [...],
  "achievements": [...]
}
```

## 🔧 Configuratie

### **.env Instellingen:**
```env
# Main Trading Bot
TELEGRAM_BOT_TOKEN=**********************************************

# Community Bot  
COMMUNITY_BOT_TOKEN=8032308171:AAGH6nJ9Dnv3gsC4GlsrMtvtzyxQlOFWvRs

# Admin
ADMIN_USER_IDS=6438349353
```

## 🎨 User Experience

### **Main Bot → Community:**
1. Gebruiker klikt "💬 Community" in main bot
2. Ziet uitleg van community features
3. Klikt "💬 Open Community Chat"
4. Telegram opent automatisch de community bot
5. Gebruiker kan direct beginnen met delen

### **Community → Main Bot:**
1. Gebruiker klikt "🔙 Terug naar Main Bot"
2. Telegram opent automatisch de main bot
3. Gebruiker is terug bij trading functies

## 🚀 Status

### **✅ Wat Werkt:**
- ✅ Beide bots online en stabiel
- ✅ Automatische navigatie tussen bots
- ✅ Photo sharing met like/comment systeem
- ✅ Automatische profit detectie
- ✅ Weekly leaderboard systeem
- ✅ Achievement tracking
- ✅ Nederlandse interface
- ✅ Professional design

### **🎯 Ready to Use:**
- Main bot: Enhanced welcome message
- Community bot: Volledig functioneel
- Automatische cross-bot navigatie
- Data persistence
- Error handling

## 📱 Test Scenario

1. **Start main bot:** `python main.py`
2. **Start community bot:** `python community_bot.py`  
3. **In Telegram main bot:** `/start` → Zie nieuwe welkomstboodschap
4. **Klik "💬 Community"** → Zie community uitleg
5. **Klik "💬 Open Community Chat"** → Ga naar community bot
6. **In community bot:** `/start` → Zie community welkom
7. **Test photo sharing:** Stuur een foto
8. **Test profit sharing:** Type "Profit: €100"
9. **Bekijk stats:** `/stats`
10. **Bekijk leaderboard:** `/leaderboard`

**Alles werkt perfect! 🎉**
