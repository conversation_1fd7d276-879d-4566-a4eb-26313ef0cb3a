#!/usr/bin/env python3
"""
Script om bot informatie op te halen
"""

import asyncio
import os
from dotenv import load_dotenv
from telegram import Bot

load_dotenv()

async def get_bot_info():
    """Get bot information"""
    
    # Main bot
    main_token = os.getenv('TELEGRAM_BOT_TOKEN')
    if main_token:
        try:
            main_bot = Bot(token=main_token)
            main_info = await main_bot.get_me()
            print(f"📱 Main Bot:")
            print(f"   Username: @{main_info.username}")
            print(f"   Name: {main_info.first_name}")
            print(f"   ID: {main_info.id}")
            print()
        except Exception as e:
            print(f"❌ Main bot error: {e}")
    
    # Community bot
    community_token = os.getenv('COMMUNITY_BOT_TOKEN')
    if community_token:
        try:
            community_bot = Bot(token=community_token)
            community_info = await community_bot.get_me()
            print(f"💬 Community Bot:")
            print(f"   Username: @{community_info.username}")
            print(f"   Name: {community_info.first_name}")
            print(f"   ID: {community_info.id}")
            print()
            
            # Generate correct links
            print(f"🔗 Correct Links:")
            print(f"   Main → Community: https://t.me/{community_info.username}")
            print(f"   Community → Main: https://t.me/{main_info.username}")
            
        except Exception as e:
            print(f"❌ Community bot error: {e}")

if __name__ == "__main__":
    asyncio.run(get_bot_info())
