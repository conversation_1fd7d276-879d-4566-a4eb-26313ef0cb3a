"""
🚀 PRACHTIGE TRADING BOT - MAIN APPLICATION
===============================================

Een geavanceerde Telegram trading bot met AI-integratie en professionele features.
Ondersteunt meerdere exchanges, strategieën en heeft een prachtige gebruikersinterface.

Author: Innovars Lab
Version: 2.0.0
"""

import asyncio
import logging
import os
import sys
from datetime import datetime
from pathlib import Path
import subprocess

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from dotenv.main import load_dotenv
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, MessageHandler, filters, ContextTypes
from telegram import Update
import dotenv

# Import our modules
from core.bot_manager import BotManager
from core.user_manager import UserManager
from database.database_manager import DatabaseManager
from utils.logger import setup_logging
from utils.config import Config

# Load environment variables
dotenv.load_dotenv()

print(dotenv.__file__)  # Should point to your venv's site-packages
# print(dotenv.__version__)  # Some versions don't have __version__

REQUIREMENTS = {
    'python-telegram-bot': '20.1',
    'numpy': '1.26.4',
    'python-dotenv': '1.0.0',
    'ccxt': '4.4.78',
    'pandas': '2.2.3',
    'python-dateutil': '2.9.0.post0'
}

def install_missing():
    logger = logging.getLogger(__name__)
    for pkg, ver in REQUIREMENTS.items():
        try:
            __import__(pkg)
            logger.info(f"{pkg} {ver} is already installed")
        except ImportError:
            logger.warning(f"Installing missing dependency: {pkg}=={ver}")
            subprocess.check_call([sys.executable, "-m", "pip", "install", f"{pkg}=={ver}"])

install_missing()

class CommunityBot:
    """Community chat bot voor gebruikersinteractie"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    async def start(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command voor community bot"""
        user = update.effective_user
        await update.message.reply_text(
            f"👋 Hallo {user.first_name}! Welkom in de Prachtige Community Chat!\n\n"
            "Hier kun je met andere traders praten, strategieën delen en vragen stellen."
        )

    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle community chat messages"""
        user = update.effective_user
        self.logger.info(f"Community message from {user.username}: {update.message.text}")

class AdminBot:
    """Admin bot voor beheer en monitoring"""
    def __init__(self):
        self.logger = logging.getLogger(__name__)

class PrachtigeTradingBot:
    """
    🚀 Hoofdklasse voor de Prachtige Trading Bot

    Deze klasse beheert alle componenten van de bot:
    - Telegram bot interface
    - Trading engine
    - User management
    - Database connections
    - AI providers
    """

    def __init__(self):
        """Initialiseer de trading bot"""
        # Setup logging
        self.logger = setup_logging()
        self.logger.info("🚀 Prachtige Trading Bot wordt opgestart...")

        # Initialize bot instances
        self.community_bot = CommunityBot()
        self.admin_bot = AdminBot()

        # Load configuration
        self.config = Config()

        # Verify required environment variables
        self._verify_environment()

        # Initialize core components
        self.database_manager = DatabaseManager()
        self.user_manager = UserManager(self.database_manager)

        # Set admin user IDs
        admin_ids = self.config.get_admin_user_ids()
        self.user_manager.set_admin_user_ids(admin_ids)

        self.bot_manager = BotManager(
            self.user_manager,
            None,  # Trading engine disabled for quick start
            self.config
        )

        # Create Telegram application
        self.application = None
        self._setup_telegram_bot()

        # Set bot manager reference in handlers and decorators
        set_bot_manager(self.bot_manager)
        set_decorators_bot_manager(self.bot_manager)

        self.logger.info("✅ Alle componenten succesvol geïnitialiseerd!")

    def _verify_environment(self):
        """Controleer of alle vereiste omgevingsvariabelen zijn ingesteld"""
        # Alleen verplichte variabelen
        required_vars = [
            'TELEGRAM_BOT_TOKEN',
            'ADMIN_USER_IDS',
        ]

        # Optionele variabelen (voor trading functies)
        optional_vars = [
            'KUCOIN_API_KEY',
            'KUCOIN_SECRET',
            'KUCOIN_PASSWORD',
            'BINANCE_API_KEY',
            'BINANCE_SECRET',
        ]

        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)

        if missing_vars:
            self.logger.error(f"❌ Ontbrekende verplichte omgevingsvariabelen: {', '.join(missing_vars)}")
            self.logger.error("📝 Controleer je .env bestand en voeg de ontbrekende variabelen toe.")
            sys.exit(1)

        # Waarschuw voor ontbrekende optionele variabelen
        missing_optional = []
        for var in optional_vars:
            if not os.getenv(var) or os.getenv(var).startswith('your_'):
                missing_optional.append(var)

        if missing_optional:
            self.logger.warning(f"⚠️ Optionele variabelen niet geconfigureerd: {', '.join(missing_optional)}")
            self.logger.warning("💡 Trading functies zijn beperkt zonder exchange API keys")

    def _setup_telegram_bot(self):
        """Setup de Telegram bot met alle handlers"""
        try:
            # Create application (simplified)
            token = os.getenv('TELEGRAM_BOT_TOKEN')
            if not token:
                raise ValueError("TELEGRAM_BOT_TOKEN niet gevonden in .env")

            # Create application with proper configuration
            builder = Application.builder()
            builder.token(token)

            # Build the application
            self.application = builder.build()

            # Simple handlers zonder complexe imports
            from telegram import Update
            from telegram.ext import ContextTypes

            async def simple_start(update: Update, context: ContextTypes.DEFAULT_TYPE):
                user = update.effective_user
                await update.message.reply_text(
                    f"🚀 **PRACHTIGE TRADING BOT** 🚀\n\n"
                    f"👋 Welkom {user.first_name}!\n"
                    f"🆔 User ID: {user.id}\n\n"
                    f"✅ Bot is online en werkend!\n"
                    f"🎯 Typ /help voor meer opties",
                    parse_mode='Markdown'
                )

            async def simple_help(update: Update, context: ContextTypes.DEFAULT_TYPE):
                await update.message.reply_text(
                    "📚 **BOT HELP** 📚\n\n"
                    "🔹 /start - Start de bot\n"
                    "🔹 /help - Toon deze help\n"
                    "🔹 /status - Bot status\n\n"
                    "🚀 **De bot is succesvol gestart!**",
                    parse_mode='Markdown'
                )

            async def simple_status(update: Update, context: ContextTypes.DEFAULT_TYPE):
                await update.message.reply_text(
                    "✅ **BOT STATUS** ✅\n\n"
                    "🤖 Status: Online\n"
                    "⚡ Versie: 2.0.0\n"
                    "📊 Database: Connected\n"
                    "🔒 Security: Active\n\n"
                    "**Alles werkt perfect!** 🎉",
                    parse_mode='Markdown'
                )

            # Add simple handlers
            self.application.add_handler(CommandHandler("start", simple_start))
            self.application.add_handler(CommandHandler("help", simple_help))
            self.application.add_handler(CommandHandler("status", simple_status))

            # Add error handler
            self.application.add_error_handler(self._error_handler)

            self.logger.info("📱 Telegram bot handlers geconfigureerd")

        except Exception as e:
            self.logger.error(f"❌ Fout bij het instellen van Telegram bot: {e}")
            sys.exit(1)

    async def _error_handler(self, update, context):
        """Handle errors that occur during bot operation"""
        self.logger.error(f"❌ Bot error: {context.error}")

        # Stuur een vriendelijk bericht naar de gebruiker
        if update and update.effective_chat:
            try:
                await context.bot.send_message(
                    chat_id=update.effective_chat.id,
                    text="🤖 Er is een technische fout opgetreden. Ons team is op de hoogte gesteld. Probeer het later opnieuw."
                )
            except Exception as send_error:
                self.logger.error(f"❌ Kon geen error bericht sturen: {send_error}")

    async def start(self):
        """Start de trading bot"""
        try:
            self.logger.info("🚀 Bot wordt gestart...")

            # Initialize database
            await self.database_manager.initialize()

            # Start the Telegram bot
            await self.application.initialize()
            await self.application.start()
            await self.application.updater.start_polling()

            self.logger.info("✅ Prachtige Trading Bot is online!")
            self.logger.info("📱 Bot is klaar om gebruikers te helpen met trading!")

            # Send startup notification to admins
            await self._send_startup_notification()

            # Keep the bot running
            await self.application.updater.idle()

        except Exception as e:
            self.logger.error(f"❌ Fout bij het starten van de bot: {e}")
            await self.stop()

    async def stop(self):
        """Stop de trading bot"""
        try:
            self.logger.info("🛑 Bot wordt gestopt...")

            # Stop Telegram bot
            if self.application:
                await self.application.stop()
                await self.application.shutdown()

            # Close database connections
            if self.database_manager:
                await self.database_manager.close()

            self.logger.info("✅ Bot succesvol gestopt")

        except Exception as e:
            self.logger.error(f"❌ Fout bij het stoppen van de bot: {e}")

    async def _send_startup_notification(self):
        """Stuur een notificatie naar admins dat de bot is gestart"""
        admin_ids = self.config.get_admin_user_ids()

        startup_message = f"""
🚀 **PRACHTIGE TRADING BOT ONLINE** 🚀

✅ **Status:** Succesvol opgestart
⏰ **Tijd:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🤖 **Versie:** 2.0.0
📊 **Features:** AI Trading, Multi-Exchange, Premium Support

**Bot is klaar voor trading! 💰**
        """

        for admin_id in admin_ids:
            try:
                await self.application.bot.send_message(
                    chat_id=admin_id,
                    text=startup_message,
                    parse_mode='Markdown'
                )
            except Exception as e:
                self.logger.warning(f"⚠️ Kon startup notificatie niet sturen naar admin {admin_id}: {e}")

def main():
    """Hoofdfunctie om de bot te starten"""
    # Toon startup banner
    print("""
╔═══════════════════════════════════════════════════════════════╗
║                                                               ║
║         🚀 PRACHTIGE TRADING BOT v2.0.0 🚀                  ║
║                                                               ║
║    ┌─ 📱 Telegram Interface                                   ║
║    ├─ 🤖 AI-Powered Trading                                  ║
║    ├─ 💰 Multi-Exchange Support                              ║
║    ├─ 📊 Advanced Strategies                                 ║
║    ├─ 💎 Premium Features                                    ║
║    └─ 🔐 Enterprise Security                                 ║
║                                                               ║
║                Made with ❤️ by Innovars Lab                  ║
╚═══════════════════════════════════════════════════════════════╝
    """)

    # Create and start bot
    bot = PrachtigeTradingBot()

    try:
        # Run the bot
        asyncio.run(bot.start())
    except KeyboardInterrupt:
        print("\n🛑 Bot gestopt door gebruiker")
    except Exception as e:
        print(f"\n❌ Onverwachte fout: {e}")
    finally:
        print("👋 Tot ziens!")

if __name__ == "__main__":
    main()

def set_bot_manager(manager):
    """Set bot manager reference in handlers"""
    try:
        from bot_telegram.telegram.handlers import set_bot_manager as set_handlers_bot_manager
        set_handlers_bot_manager(manager)
    except ImportError as e:
        logger.warning(f"Could not set bot manager in handlers: {e}")

def set_decorators_bot_manager(manager):
    """Set bot manager reference in decorators"""
    try:
        from utils.decorators import set_bot_manager as set_decorators_manager
        set_decorators_manager(manager)
    except ImportError as e:
        logger.warning(f"Could not set bot manager in decorators: {e}")
