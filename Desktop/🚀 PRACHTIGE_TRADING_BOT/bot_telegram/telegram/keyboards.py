"""
🎹 TELEGRAM KEYBOARDS - Keyboard Definitions
==========================================

Alle keyboard definities voor de Prachtige Trading Bot.
"""

from telegram import InlineKeyboardButton, InlineKeyboardMarkup, ReplyKeyboardMarkup, KeyboardButton

def get_main_keyboard(user_id=None):
    """🎨 Hoofdmenu keyboard - Prachtig en gebruiksvriendelijk"""
    keyboard = [
        [
            InlineKeyboardButton("📊 Trading Hub", callback_data="trading_dashboard"),
            InlineKeyboardButton("💰 Portfolio", callback_data="user_portfolio")
        ],
        [
            InlineKeyboardButton("🎯 Live Signals", callback_data="trading_signals"),
            InlineKeyboardButton("📈 Market Data", callback_data="market_data")
        ],
        [
            InlineKeyboardButton("🤖 AI Assistant", callback_data="ai_assistant"),
            InlineKeyboardButton("⚡ Auto Trading", callback_data="toggle_auto_trading")
        ],
        [
            InlineKeyboardButton("💬 Community", callback_data="community_chat"),
            InlineKeyboardButton("📚 Learning", callback_data="learning_center")
        ],
        [
            InlineKeyboardButton("⚙️ Settings", callback_data="account_settings"),
            InlineKeyboardButton("💎 Premium", callback_data="upgrade_vip")
        ],
        [
            InlineKeyboardButton("🆘 Support", callback_data="support_center"),
            InlineKeyboardButton("ℹ️ About", callback_data="about_bot")
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_trading_keyboard():
    """📊 Trading Hub - Uitgebreide trading functies"""
    keyboard = [
        [
            InlineKeyboardButton("🛒 Quick Buy", callback_data="quick_buy"),
            InlineKeyboardButton("💰 Quick Sell", callback_data="quick_sell")
        ],
        [
            InlineKeyboardButton("📊 Market Order", callback_data="market_order"),
            InlineKeyboardButton("⏱️ Limit Order", callback_data="limit_order")
        ],
        [
            InlineKeyboardButton("🎯 Stop Loss", callback_data="stop_loss_order"),
            InlineKeyboardButton("📈 Take Profit", callback_data="take_profit_order")
        ],
        [
            InlineKeyboardButton("📋 Open Positions", callback_data="open_positions"),
            InlineKeyboardButton("📜 Trade History", callback_data="trade_history")
        ],
        [
            InlineKeyboardButton("🔄 Refresh Prices", callback_data="refresh_prices"),
            InlineKeyboardButton("⚙️ Trading Settings", callback_data="trading_settings")
        ],
        [
            InlineKeyboardButton("🔙 Back to Menu", callback_data="user_main")
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_portfolio_keyboard():
    """Portfolio keyboard"""
    keyboard = [
        [
            InlineKeyboardButton("💰 Balans", callback_data="portfolio_balance"),
            InlineKeyboardButton("📊 Performance", callback_data="portfolio_performance")
        ],
        [
            InlineKeyboardButton("📈 Assets", callback_data="portfolio_assets"),
            InlineKeyboardButton("💹 PnL", callback_data="portfolio_pnl")
        ],
        [
            InlineKeyboardButton("🔄 Vernieuwen", callback_data="refresh_portfolio"),
            InlineKeyboardButton("🔙 Terug", callback_data="user_main")
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_exchange_keyboard():
    """Exchange selectie keyboard"""
    keyboard = [
        [
            InlineKeyboardButton("🟡 KuCoin", callback_data="setup_kucoin"),
            InlineKeyboardButton("🟠 Binance", callback_data="setup_binance")
        ],
        [
            InlineKeyboardButton("🔙 Terug", callback_data="user_main")
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_admin_keyboard():
    """Admin dashboard keyboard"""
    keyboard = [
        [
            InlineKeyboardButton("👥 Gebruikers", callback_data="admin_users"),
            InlineKeyboardButton("📊 Analytics", callback_data="admin_analytics")
        ],
        [
            InlineKeyboardButton("📢 Broadcast", callback_data="admin_broadcast"),
            InlineKeyboardButton("⚙️ Instellingen", callback_data="admin_settings")
        ],
        [
            InlineKeyboardButton("💹 Trading Controls", callback_data="trading_controls"),
            InlineKeyboardButton("🔙 Terug", callback_data="user_main")
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_chat_main_keyboard():
    """Community chat keyboard"""
    keyboard = [
        [
            InlineKeyboardButton("🚀 Algemene Chat", callback_data="general_chat"),
            InlineKeyboardButton("📊 Signalen Chat", callback_data="signals_chat")
        ],
        [
            InlineKeyboardButton("🎯 Strategieën", callback_data="strategies_chat"),
            InlineKeyboardButton("📚 Educatie", callback_data="education_chat")
        ],
        [
            InlineKeyboardButton("🔙 Terug", callback_data="user_main")
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_permanent_keyboard():
    """🎯 Permanente keyboard met menu knop en snelle toegang"""
    keyboard = [
        [
            KeyboardButton("📋 Menu"),
            KeyboardButton("📊 Trading"),
            KeyboardButton("💰 Portfolio")
        ],
        [
            KeyboardButton("🎯 Signalen"),
            KeyboardButton("💬 Community"),
            KeyboardButton("📈 Market")
        ],
        [
            KeyboardButton("🤖 AI Assistant"),
            KeyboardButton("⚙️ Settings"),
            KeyboardButton("🆘 Help")
        ]
    ]
    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=False)

def get_user_persistent_keyboard():
    """Persistent keyboard voor gewone gebruikers"""
    keyboard = [
        [KeyboardButton("💹 Trading"), KeyboardButton("📊 Portfolio")],
        [KeyboardButton("📈 Signalen"), KeyboardButton("💬 Chat")]
    ]
    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)

def get_admin_persistent_keyboard():
    """Persistent keyboard voor admin gebruikers"""
    keyboard = [
        [KeyboardButton("👑 Admin Dashboard"), KeyboardButton("💹 Trading")],
        [KeyboardButton("📊 Analytics"), KeyboardButton("⚙️ Instellingen")]
    ]
    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)

# ===============================================================================
# 🆕 NIEUWE KEYBOARDS VOOR UITGEBREIDE FUNCTIES
# ===============================================================================

def get_market_data_keyboard():
    """📈 Market Data keyboard"""
    keyboard = [
        [
            InlineKeyboardButton("₿ Bitcoin (BTC)", callback_data="market_btc"),
            InlineKeyboardButton("⟠ Ethereum (ETH)", callback_data="market_eth")
        ],
        [
            InlineKeyboardButton("🔥 Top Gainers", callback_data="market_gainers"),
            InlineKeyboardButton("📉 Top Losers", callback_data="market_losers")
        ],
        [
            InlineKeyboardButton("📊 Market Overview", callback_data="market_overview"),
            InlineKeyboardButton("🔍 Search Coin", callback_data="market_search")
        ],
        [
            InlineKeyboardButton("📈 Trending", callback_data="market_trending"),
            InlineKeyboardButton("💰 Market Cap", callback_data="market_cap")
        ],
        [
            InlineKeyboardButton("🔙 Back to Menu", callback_data="user_main")
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_ai_assistant_keyboard():
    """🤖 AI Assistant keyboard"""
    keyboard = [
        [
            InlineKeyboardButton("💡 Trading Advice", callback_data="ai_trading_advice"),
            InlineKeyboardButton("📊 Market Analysis", callback_data="ai_market_analysis")
        ],
        [
            InlineKeyboardButton("🎯 Strategy Help", callback_data="ai_strategy_help"),
            InlineKeyboardButton("📚 Educational Q&A", callback_data="ai_education")
        ],
        [
            InlineKeyboardButton("🔮 Price Prediction", callback_data="ai_prediction"),
            InlineKeyboardButton("⚠️ Risk Assessment", callback_data="ai_risk_assessment")
        ],
        [
            InlineKeyboardButton("💬 Chat with AI", callback_data="ai_chat"),
            InlineKeyboardButton("🔙 Back to Menu", callback_data="user_main")
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_learning_center_keyboard():
    """📚 Learning Center keyboard"""
    keyboard = [
        [
            InlineKeyboardButton("🎓 Trading Basics", callback_data="learn_basics"),
            InlineKeyboardButton("📈 Technical Analysis", callback_data="learn_technical")
        ],
        [
            InlineKeyboardButton("💰 Risk Management", callback_data="learn_risk"),
            InlineKeyboardButton("🎯 Trading Strategies", callback_data="learn_strategies")
        ],
        [
            InlineKeyboardButton("🤖 Bot Usage Guide", callback_data="learn_bot"),
            InlineKeyboardButton("📊 Market Psychology", callback_data="learn_psychology")
        ],
        [
            InlineKeyboardButton("🏆 Trading Quiz", callback_data="learn_quiz"),
            InlineKeyboardButton("📖 Glossary", callback_data="learn_glossary")
        ],
        [
            InlineKeyboardButton("🔙 Back to Menu", callback_data="user_main")
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_support_center_keyboard():
    """🆘 Support Center keyboard"""
    keyboard = [
        [
            InlineKeyboardButton("❓ FAQ", callback_data="support_faq"),
            InlineKeyboardButton("📞 Contact Support", callback_data="support_contact")
        ],
        [
            InlineKeyboardButton("🐛 Report Bug", callback_data="support_bug"),
            InlineKeyboardButton("💡 Feature Request", callback_data="support_feature")
        ],
        [
            InlineKeyboardButton("📋 User Guide", callback_data="support_guide"),
            InlineKeyboardButton("🔧 Troubleshooting", callback_data="support_troubleshoot")
        ],
        [
            InlineKeyboardButton("💬 Live Chat", callback_data="support_live_chat"),
            InlineKeyboardButton("🔙 Back to Menu", callback_data="user_main")
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_settings_keyboard():
    """⚙️ Settings keyboard"""
    keyboard = [
        [
            InlineKeyboardButton("🔔 Notifications", callback_data="settings_notifications"),
            InlineKeyboardButton("🌍 Language", callback_data="settings_language")
        ],
        [
            InlineKeyboardButton("🔐 Security", callback_data="settings_security"),
            InlineKeyboardButton("🔑 API Keys", callback_data="settings_api_keys")
        ],
        [
            InlineKeyboardButton("⚡ Auto Trading", callback_data="settings_auto_trading"),
            InlineKeyboardButton("📊 Display Preferences", callback_data="settings_display")
        ],
        [
            InlineKeyboardButton("💾 Backup Data", callback_data="settings_backup"),
            InlineKeyboardButton("🗑️ Delete Account", callback_data="settings_delete")
        ],
        [
            InlineKeyboardButton("🔙 Back to Menu", callback_data="user_main")
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

# Export all keyboards
__all__ = [
    'get_main_keyboard',
    'get_trading_keyboard',
    'get_portfolio_keyboard',
    'get_admin_keyboard',
    'get_exchange_keyboard',
    'get_chat_main_keyboard',
    'get_permanent_keyboard',
    'get_user_persistent_keyboard',
    'get_admin_persistent_keyboard',
    'get_market_data_keyboard',
    'get_ai_assistant_keyboard',
    'get_learning_center_keyboard',
    'get_support_center_keyboard',
    'get_settings_keyboard'
]