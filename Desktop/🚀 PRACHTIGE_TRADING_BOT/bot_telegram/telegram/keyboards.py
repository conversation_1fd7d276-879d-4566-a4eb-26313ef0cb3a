"""
🎹 TELEGRAM KEYBOARDS - Keyboard Definitions
==========================================

Alle keyboard definities voor de Prachtige Trading Bot.
"""

from telegram import InlineKeyboardButton, InlineKeyboardMarkup, ReplyKeyboardMarkup, KeyboardButton

def get_main_keyboard(user_id=None):
    """Hoofdmenu keyboard voor alle gebruikers"""
    keyboard = [
        [
            InlineKeyboardButton("📈 Trading", callback_data="trading_dashboard"),
            InlineKeyboardButton("📊 Portfolio", callback_data="user_portfolio")
        ],
        [
            InlineKeyboardButton("🎯 Signalen", callback_data="trading_signals"),
            InlineKeyboardButton("💬 Community", callback_data="community_chat")
        ],
        [
            InlineKeyboardButton("⚡ Auto Trading", callback_data="toggle_auto_trading"),
            InlineKeyboardButton("⚙️ Instellingen", callback_data="account_settings")
        ],
        [
            InlineKeyboardButton("❓ Help", callback_data="help_center"),
            InlineKeyboardButton("💎 Premium", callback_data="upgrade_vip")
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_trading_keyboard():
    """Trading dashboard keyboard"""
    keyboard = [
        [
            InlineKeyboardButton("🛒 Koop", callback_data="quick_buy"),
            InlineKeyboardButton("💰 Verkoop", callback_data="quick_sell")
        ],
        [
            InlineKeyboardButton("📊 Markt Orde", callback_data="market_order"),
            InlineKeyboardButton("⏱️ Limit Orde", callback_data="limit_order")
        ],
        [
            InlineKeyboardButton("📈 Open Posities", callback_data="open_positions"),
            InlineKeyboardButton("📜 Geschiedenis", callback_data="trade_history")
        ],
        [
            InlineKeyboardButton("🔙 Terug", callback_data="user_main")
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_portfolio_keyboard():
    """Portfolio keyboard"""
    keyboard = [
        [
            InlineKeyboardButton("💰 Balans", callback_data="portfolio_balance"),
            InlineKeyboardButton("📊 Performance", callback_data="portfolio_performance")
        ],
        [
            InlineKeyboardButton("📈 Assets", callback_data="portfolio_assets"),
            InlineKeyboardButton("💹 PnL", callback_data="portfolio_pnl")
        ],
        [
            InlineKeyboardButton("🔄 Vernieuwen", callback_data="refresh_portfolio"),
            InlineKeyboardButton("🔙 Terug", callback_data="user_main")
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_exchange_keyboard():
    """Exchange selectie keyboard"""
    keyboard = [
        [
            InlineKeyboardButton("🟡 KuCoin", callback_data="setup_kucoin"),
            InlineKeyboardButton("🟠 Binance", callback_data="setup_binance")
        ],
        [
            InlineKeyboardButton("🔙 Terug", callback_data="user_main")
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_admin_keyboard():
    """Admin dashboard keyboard"""
    keyboard = [
        [
            InlineKeyboardButton("👥 Gebruikers", callback_data="admin_users"),
            InlineKeyboardButton("📊 Analytics", callback_data="admin_analytics")
        ],
        [
            InlineKeyboardButton("📢 Broadcast", callback_data="admin_broadcast"),
            InlineKeyboardButton("⚙️ Instellingen", callback_data="admin_settings")
        ],
        [
            InlineKeyboardButton("💹 Trading Controls", callback_data="trading_controls"),
            InlineKeyboardButton("🔙 Terug", callback_data="user_main")
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_chat_main_keyboard():
    """Community chat keyboard"""
    keyboard = [
        [
            InlineKeyboardButton("🚀 Algemene Chat", callback_data="general_chat"),
            InlineKeyboardButton("📊 Signalen Chat", callback_data="signals_chat")
        ],
        [
            InlineKeyboardButton("🎯 Strategieën", callback_data="strategies_chat"),
            InlineKeyboardButton("📚 Educatie", callback_data="education_chat")
        ],
        [
            InlineKeyboardButton("🔙 Terug", callback_data="user_main")
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_user_persistent_keyboard():
    """Persistent keyboard voor gewone gebruikers"""
    keyboard = [
        [KeyboardButton("💹 Trading"), KeyboardButton("📊 Portfolio")],
        [KeyboardButton("📈 Signalen"), KeyboardButton("💬 Chat")]
    ]
    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)

def get_admin_persistent_keyboard():
    """Persistent keyboard voor admin gebruikers"""
    keyboard = [
        [KeyboardButton("👑 Admin Dashboard"), KeyboardButton("💹 Trading")],
        [KeyboardButton("📊 Analytics"), KeyboardButton("⚙️ Instellingen")]
    ]
    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)