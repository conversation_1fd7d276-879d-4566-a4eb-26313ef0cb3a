"""
Main Telegram Bot Handlers for Prachtige Trading Bot
Integrates all functionality from keyboards, strategies, and database
"""

import logging
from telegram import Update
from telegram.ext import ContextTypes
from typing import Dict, Any

from .keyboards import *
from src.strategies.technical.rsi_strategy import RSIStrategy
from src.database.initialize_db import create_trading_db
from config.config import Config

logger = logging.getLogger(__name__)

class TradingBotHandlers:
    """Main handler class for all bot functionality"""
    
    def __init__(self):
        self.active_strategies: Dict[str, Any] = {}
        self.user_sessions: Dict[int, Dict] = {}
        
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command"""
        user = update.effective_user
        
        # Initialize user session
        self.user_sessions[user.id] = {
            'username': user.username,
            'first_name': user.first_name,
            'is_admin': user.id in Config.ADMIN_IDS,
            'active_strategies': []
        }
        
        welcome_text = f"""
🚀 *Welkom bij Prachtige Trading Bot!*

Hallo {user.first_name}! 👋

🎯 *Wat kun je doen:*
• 💹 Automatisch handelen met AI strategieën
• 📊 Portfolio beheren en analyseren  
• 📈 Real-time handelssignalen ontvangen
• 🤖 RSI, MACD, ML strategieën gebruiken
• 💬 Deel signalen in de community

🔥 *Ondersteunde Exchanges:*
• Binance, KuCoin, OKX, ByBit

Kies een optie hieronder om te beginnen! 👇
        """
        
        keyboard = get_main_keyboard()
        await update.message.reply_text(
            welcome_text, 
            reply_markup=keyboard,
            parse_mode='Markdown'
        )

    async def handle_callback_query(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle all callback queries from inline keyboards"""
        query = update.callback_query
        await query.answer()
        
        data = query.data
        user_id = query.from_user.id
        
        # Route to appropriate handler based on callback data
        if data == "user_main":
            await self.show_main_menu(query, context)
        elif data == "user_trading":
            await self.show_trading_menu(query, context)
        elif data == "user_portfolio":
            await self.show_portfolio_menu(query, context)
        elif data == "user_strategies":
            await self.show_strategies_menu(query, context)
        elif data.startswith("strategy_"):
            await self.handle_strategy_action(query, context, data)
        elif data.startswith("quick_trade_"):
            await self.handle_quick_trade(query, context, data)
        elif data == "account_settings":
            await self.show_settings_menu(query, context)
        # Add more handlers as needed
            
    async def show_main_menu(self, query, context):
        """Show main menu"""
        text = """
🏠 *Hoofdmenu*

Welkom terug! Kies wat je wilt doen:

💹 *Trading* - Start met handelen
📊 *Portfolio* - Bekijk je balans en posities  
📈 *Signals* - Ontvang handelssignalen
🤖 *Strategies* - Beheer je trading strategieën
💬 *Community* - Chat met andere traders
⚙️ *Settings* - Pas je instellingen aan
        """
        
        keyboard = get_main_keyboard()
        await query.edit_message_text(
            text, 
            reply_markup=keyboard,
            parse_mode='Markdown'
        )

    async def show_strategies_menu(self, query, context):
        """Show strategies management menu"""
        user_id = query.from_user.id
        
        # Get user's active strategies (from database)
        active_count = len(self.user_sessions.get(user_id, {}).get('active_strategies', []))
        
        text = f"""
🤖 *Strategy Management*

*Actieve Strategieën:* {active_count}

*Beschikbare Strategieën:*
🎯 RSI Strategy - Relative Strength Index
📊 MACD Strategy - Moving Average Convergence  
📈 MA Strategy - Moving Averages