"""
Telegram keyboards for the Prachtige Trading Bot.
Contains all keyboard layouts for the Telegram bot interface.
"""

from telegram import InlineKeyboardButton, InlineKeyboardMarkup, ReplyKeyboardMarkup, KeyboardButton

def get_main_keyboard():
    """Hoofdmenu voor alle gebruikers"""
    keyboard = [
        [
            InlineKeyboardButton("📈 Handel", callback_data="trading_dashboard"),
            InlineKeyboardButton("📊 Portefeuille", callback_data="portfolio_overview")
        ],
        [
            InlineKeyboardButton("⚡ Snelle Acties", callback_data="quick_actions"),
            InlineKeyboardButton("📋 Mijn Strategieën", callback_data="my_strategies")
        ],
        [
            InlineKeyboardButton("📊 Marktanalyse", callback_data="market_analysis"),
            InlineKeyboardButton("🔔 Meldingen", callback_data="notifications")
        ],
        [
            InlineKeyboardButton("⚙️ Instellingen", callback_data="settings"),
            InlineKeyboardButton("❓ Help", callback_data="help_center")
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_admin_keyboard():
    """Admin dashboard keyboard"""
    keyboard = [
        [InlineKeyboardButton("👥 User Management", callback_data="admin_users"),
         InlineKeyboardButton("📊 Analytics", callback_data="admin_analytics")],
        [InlineKeyboardButton("📢 Broadcast", callback_data="admin_broadcast"),
         InlineKeyboardButton("⚙️ Bot Settings", callback_data="admin_settings")],
        [InlineKeyboardButton("💹 Trading Controls", callback_data="admin_trading"),
         InlineKeyboardButton("🔙 Exit Admin", callback_data="user_main")]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_trading_keyboard():
    """Handelsdashboard toetsenbord"""
    keyboard = [
        # Eerste rij: Snelle acties
        [
            InlineKeyboardButton("🛒 Koop", callback_data="quick_buy"),
            InlineKeyboardButton("💰 Verkoop", callback_data="quick_sell"),
            InlineKeyboardButton("⏹️ Sluit Alle", callback_data="close_all")
        ],
        # Tweede rij: Handelsacties
        [
            InlineKeyboardButton("📊 Markt Orde", callback_data="market_order"),
            InlineKeyboardButton("⏱️ Limit Orde", callback_data="limit_order")
        ],
        # Derde rij: Posities en instellingen
        [
            InlineKeyboardButton("📈 Openstaand", callback_data="open_positions"),
            InlineKeyboardButton("📜 Geschiedenis", callback_data="trade_history")
        ],
        # Vierde rij: Geavanceerd en terug
        [
            InlineKeyboardButton("⚙️ Handelsinstellingen", callback_data="trading_settings"),
            InlineKeyboardButton("❓ Hulp", callback_data="trading_help")
        ],
        # Vijfde rij: Terug naar hoofdmenu
        [InlineKeyboardButton("🔙 Terug naar Hoofdmenu", callback_data="main_menu")]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_strategy_keyboard():
    """Strategiebeheer toetsenbord"""
    keyboard = [
        # Eerste rij: Strategie acties
        [
            InlineKeyboardButton("➕ Nieuwe Strategie", callback_data="strategy_create"),
            InlineKeyboardButton("📋 Mijn Strategieën", callback_data="strategy_list")
        ],
        # Tweede rij: Strategie sjablonen
        [
            InlineKeyboardButton("📊 Populair", callback_data="strategy_popular"),
            InlineKeyboardButton("🏆 Aanbevolen", callback_data="strategy_recommended")
        ],
        # Derde rij: Geavanceerde opties
        [
            InlineKeyboardButton("⚙️ Instellingen", callback_data="strategy_settings"),
            InlineKeyboardButton("📊 Prestaties", callback_data="strategy_performance")
        ],
        # Vierde rij: Terug naar hoofdmenu
        [InlineKeyboardButton("🔙 Terug naar Hoofdmenu", callback_data="main_menu")]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_portfolio_keyboard():
    """Portfolio dashboard toetsenbord"""
    keyboard = [
        [
            InlineKeyboardButton("📊 Prestatie", callback_data="portfolio_performance"),
            InlineKeyboardButton("💰 Balans", callback_data="portfolio_balance")
        ],
        [
            InlineKeyboardButton("📈 Openstaand", callback_data="open_positions"),
         InlineKeyboardButton("📜 Trade History", callback_data="trade_history")],
        [InlineKeyboardButton("💸 Deposit/Withdraw", callback_data="portfolio_deposit"),
         InlineKeyboardButton("🔙 Back", callback_data="user_main")]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_exchange_keyboard():
    """Exchange selection keyboard"""
    keyboard = [
        [InlineKeyboardButton("🟢 KuCoin", callback_data="exchange_kucoin"),
         InlineKeyboardButton("🟡 Binance", callback_data="exchange_binance")],
        [InlineKeyboardButton("🔵 OKX", callback_data="exchange_okx"),
         InlineKeyboardButton("🟠 ByBit", callback_data="exchange_bybit")],
        [InlineKeyboardButton("🔙 Back", callback_data="user_main")]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_chat_main_keyboard():
    """Community chat keyboard"""
    keyboard = [
        [InlineKeyboardButton("🚀 General Trading", callback_data="chat_general"),
         InlineKeyboardButton("📊 Signal Discussion", callback_data="chat_signals")],
        [InlineKeyboardButton("🎯 Strategy Sharing", callback_data="chat_strategies"),
         InlineKeyboardButton("📚 Education", callback_data="chat_education")],
        [InlineKeyboardButton("🔙 Back to Main", callback_data="user_main")]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_quick_actions_keyboard():
    """Sneltoetsen voor snelle handelsacties"""
    keyboard = [
        # Eerste rij: Snelle koop/verkoop
        [
            InlineKeyboardButton("🔼 1% Koop", callback_data="quick_buy_1"),
            InlineKeyboardButton("🔽 1% Verkoop", callback_data="quick_sell_1")
        ],
        # Tweede rij: Grotere bedragen
        [
            InlineKeyboardButton("🔼 5% Koop", callback_data="quick_buy_5"),
            InlineKeyboardButton("🔽 5% Verkoop", callback_data="quick_sell_5")
        ],
        # Derde rij: Snelle acties
        [
            InlineKeyboardButton("⏹️ Sluit Alle", callback_data="close_all"),
            InlineKeyboardButton("🔄 Vernieuw", callback_data="refresh")
        ],
        # Vierde rij: Terug naar hoofdmenu
        [InlineKeyboardButton("🔙 Terug naar Hoofdmenu", callback_data="main_menu")]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_user_main_keyboard():
    """Legacy keyboard - redirects to main keyboard"""
    return get_main_keyboard()

def get_ceo_main_keyboard():
    """CEO dashboard keyboard (voor super admins)"""
    keyboard = [
        [
            InlineKeyboardButton("👥 Actieve Gebruikers", callback_data="ceo_users"),
            InlineKeyboardButton("💰 Totaal W&V", callback_data="ceo_pnl")
        ],
        [
            InlineKeyboardButton("🤖 Bot Status", callback_data="ceo_bots"),
            InlineKeyboardButton("📊 Analyse", callback_data="ceo_analytics")
        ],
        [
            InlineKeyboardButton("📢 Broadcast", callback_data="admin_broadcast"),
            InlineKeyboardButton("⚙️ Instellingen", callback_data="admin_settings")
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_user_persistent_keyboard():
    """Persistent keyboard for regular users"""
    keyboard = [
        [KeyboardButton("💹 Trading"), KeyboardButton("📊 Portfolio")],
        [KeyboardButton("📈 Signals"), KeyboardButton("💬 Chat")]
    ]
    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)

def get_ceo_persistent_keyboard():
    """Persistent keyboard for admin users"""
    keyboard = [
        [KeyboardButton("👑 CEO Dashboard"), KeyboardButton("💹 Trading")],
        [KeyboardButton("📊 Analytics"), KeyboardButton("⚙️ Settings")]
    ]
    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)
