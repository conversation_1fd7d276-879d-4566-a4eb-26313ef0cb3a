"""
Telegram handlers for the Prachtige Trading Bot.
Contains all command and callback handlers for the Telegram bot.
"""

import logging
from typing import Dict, List, Optional

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.constants import ParseMode
from telegram.ext import ContextTypes

# Initialize logger
logger = logging.getLogger(__name__)

# Bot manager reference
bot_manager = None

def set_bot_manager(manager):
    """Set the bot manager reference"""
    global bot_manager
    bot_manager = manager

# Helper functions
def format_currency(amount: float) -> str:
    """Format amount as currency"""
    return f"${amount:,.2f}"

def format_percentage(value: float) -> str:
    """Format value as percentage"""
    return f"{value:.1f}%"

# Command handlers
async def start_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle /start command"""
    user = update.effective_user
    user_id = user.id

    # Register user if not exists
    await bot_manager.user_manager.register_user(
        user_id=user_id,
        username=user.username,
        first_name=user.first_name,
        last_name=user.last_name
    )

    # Check if user is admin
    is_admin = await bot_manager.user_manager.is_admin(user_id)

    if is_admin:
        # Show admin dashboard
        await admin_handler(update, context)
    else:
        # Show user dashboard
        from bot_telegram.keyboards import get_main_keyboard

        text = f"""
🚀 **WELCOME TO PRACHTIGE TRADING BOT!** 🚀

Hello {user.first_name}! I'm your personal crypto trading assistant.

🤖 **What I can do:**
├─ 📈 Execute trades on your behalf
├─ 💰 Track your portfolio performance
├─ 🎯 Provide trading signals
├─ 📊 Analyze market trends
└─ 💬 Connect with other traders

**Choose an option below to get started:**
        """

        await update.message.reply_text(
            text,
            reply_markup=get_main_keyboard(),
            parse_mode=ParseMode.MARKDOWN
        )

async def help_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle /help command"""
    text = """
❓ **HELP & SUPPORT** ❓

🤖 **Bot Commands:**
├─ /start - Start the bot
├─ /help - Show this help message
├─ /register - Configure API keys
├─ /trading - Trading dashboard
└─ /admin - Admin panel (admins only)

🔧 **Common Issues:**
├─ API keys not working? Double-check for typos
├─ Bot not responding? Try /start to reset
├─ Trades not executing? Check your balance
└─ Need more help? Contact support

**Need assistance? Contact us:**
<EMAIL>
    """

    keyboard = [
        [InlineKeyboardButton("📚 User Guide", callback_data="user_guide")],
        [InlineKeyboardButton("🔧 Troubleshooting", callback_data="troubleshooting")],
        [InlineKeyboardButton("💬 Contact Support", callback_data="contact_support")],
        [InlineKeyboardButton("🔙 Back to Main", callback_data="user_main")]
    ]

    await update.message.reply_text(
        text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode=ParseMode.MARKDOWN
    )

async def register_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle /register command"""
    from bot_telegram.keyboards import get_exchange_keyboard

    text = """
🔑 **API KEY REGISTRATION** 🔑

To enable trading, you need to connect your exchange API keys.

⚠️ **Security Notes:**
├─ We encrypt all API keys
├─ Enable "Read + Trade" permissions only
├─ Disable withdrawals for security
└─ Never share your API keys with anyone

**Select an exchange to connect:**
    """

    await update.message.reply_text(
        text,
        reply_markup=get_exchange_keyboard(),
        parse_mode=ParseMode.MARKDOWN
    )

async def trading_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle /trading command"""
    from bot_telegram.keyboards import get_trading_keyboard

    text = """
📈 **TRADING DASHBOARD** 📈

Welcome to your trading control center!

🚀 **Trading Actions:**
├─ 💰 View Portfolio
├─ 📊 Trading Signals
├─ 🤖 Auto Trading
├─ 📈 Manual Trading
└─ 📉 Open Positions

**Select an option:**
    """

    await update.message.reply_text(
        text,
        reply_markup=get_trading_keyboard(),
        parse_mode=ParseMode.MARKDOWN
    )

async def admin_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle /admin command"""
    user_id = update.effective_user.id

    # Check if user is admin
    is_admin = await bot_manager.user_manager.is_admin(user_id)

    if is_admin:
        from bot_telegram.keyboards import get_admin_keyboard

        text = """
👑 **ADMIN DASHBOARD** 👑

Welcome to the admin control panel!

🔧 **Admin Actions:**
├─ 👥 User Management
├─ 📊 System Analytics
├─ 📢 Broadcast Messages
├─ ⚙️ Bot Settings
└─ 💹 Trading Controls

**Select an option:**
        """

        await update.message.reply_text(
            text,
            reply_markup=get_admin_keyboard(),
            parse_mode=ParseMode.MARKDOWN
        )
    else:
        await update.message.reply_text(
            "❌ You don't have permission to access the admin panel."
        )

async def callback_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle callback queries from inline keyboards"""
    query = update.callback_query
    await query.answer()

    # Get callback data
    data = query.data

    # Process callback based on data
    if data == "user_portfolio":
        await _send_portfolio_overview(update, context)
    elif data == "trading_signals":
        await _send_signals_dashboard(update, context)
    elif data == "community_chat":
        await _send_community_chat(update, context)
    elif data == "toggle_auto_trading":
        await _toggle_auto_trading(update, context)
    elif data == "account_settings":
        await _send_account_settings(update, context)
    elif data == "admin_analytics":
        await _send_analytics_dashboard(update, context)
    else:
        # Default response for unhandled callbacks
        await query.message.reply_text(f"Action '{data}' not implemented yet.")

async def message_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle text messages"""
    text = update.message.text
    user_id = update.effective_user.id
    state = context.user_data.get('state')

    # Process message based on user state
    if state == 'waiting_api_key':
        await _process_api_key_input(update, context, text)
    elif state == 'waiting_api_secret':
        await _process_api_secret_input(update, context, text)
    elif state == 'waiting_api_passphrase':
        await _process_api_passphrase_input(update, context, text)
    elif state == 'waiting_trade_amount':
        await _process_trade_amount_input(update, context, text)
    elif state == 'waiting_broadcast_message':
        await _process_broadcast_message(update, context, text)
    else:
        # Default response for unhandled messages
        await update.message.reply_text(
            "I'm not sure how to respond to that. Please use the buttons or commands."
        )

# Admin decorator
def admin_required(func):
    """Decorator to check if user is admin"""
    async def wrapper(update: Update, context: ContextTypes.DEFAULT_TYPE, *args, **kwargs):
        user_id = update.effective_user.id
        is_admin = await bot_manager.user_manager.is_admin(user_id)

        if is_admin:
            return await func(update, context, *args, **kwargs)
        else:
            await update.message.reply_text(
                "❌ You don't have permission to perform this action."
            )

    return wrapper

async def _send_portfolio_overview(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Send portfolio overview"""
    user_id = update.effective_user.id

    try:
        user_stats = await bot_manager.user_manager.get_user_stats(user_id)

        # Get portfolio data from trading engine
        portfolio_value = await bot_manager.trading_engine.get_portfolio_value(user_id)
        current_positions = user_stats.get('open_positions', 0)

        text = f"""
📊 **PORTFOLIO OVERVIEW** 📊

💰 **Current Status:**
├─ 💵 Total Value: {format_currency(portfolio_value)}
├─ 📈 Open Positions: {current_positions}
├─ 💸 Today's PnL: {format_currency(user_stats.get('daily_pnl', 0))}
├─ 🎯 Total PnL: {format_currency(user_stats.get('total_pnl', 0))}
└─ 📊 Win Rate: {format_percentage(user_stats.get('win_rate', 0))}

📈 **Performance:**
├─ 🔢 Total Trades: {user_stats.get('total_trades', 0)}
├─ ✅ Winning Trades: {user_stats.get('winning_trades', 0)}
├─ 💰 Total Volume: {format_currency(user_stats.get('total_volume', 0))}
└─ 📅 Member Since: {user_stats.get('member_since', 'Unknown')}

**Portfolio Actions:**
        """

        from bot_telegram.keyboards import get_portfolio_keyboard
        await update.message.reply_text(
            text,
            reply_markup=get_portfolio_keyboard(),
            parse_mode=ParseMode.MARKDOWN
        )

    except Exception as e:
        logger.error(f"❌ Error sending portfolio overview: {e}")
        await update.message.reply_text("❌ Could not load portfolio data.")

async def _send_signals_dashboard(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Send trading signals dashboard"""
    user_id = update.effective_user.id

    try:
        is_premium = await bot_manager.user_manager.is_premium(user_id)

        # Get latest signals
        signals = await _get_latest_signals(limit=5)

        text = f"""
📈 **TRADING SIGNALS** 📈

🔥 **Live Market Signals:**

"""

        for i, signal in enumerate(signals, 1):
            trend_emoji = "🚀" if signal['trend'] == 'bullish' else "📉"
            text += f"{i}. {trend_emoji} **{signal['symbol']}** - {signal['action']}\n"
            text += f"   💰 Price: ${signal['price']} | 🎯 Confidence: {signal['confidence']}%\n\n"

        if not is_premium:
            text += """
💎 **Premium Signals Available:**
├─ 🤖 AI-Powered Analysis
├─ 📊 Multi-Timeframe Signals
├─ 🎯 Custom Strategy Alerts
└─ ⚡ Real-time Notifications

**Upgrade for full access!**
            """

        keyboard = [
            [InlineKeyboardButton("🔄 Refresh Signals", callback_data="refresh_signals")],
            [InlineKeyboardButton("⚙️ Signal Settings", callback_data="signal_settings")],
            [InlineKeyboardButton("💎 Upgrade to Premium", callback_data="upgrade_vip")] if not is_premium else [],
            [InlineKeyboardButton("🔙 Back", callback_data="user_main")]
        ]

        # Remove empty lists
        keyboard = [row for row in keyboard if row]

        await update.message.reply_text(
            text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode=ParseMode.MARKDOWN
        )

    except Exception as e:
        logger.error(f"❌ Error sending signals dashboard: {e}")
        await update.message.reply_text("❌ Could not load signals data.")

async def _send_community_chat(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Send community chat interface"""
    text = """
💬 **TRADING COMMUNITY** 💬

🌟 **Join the Discussion:**
├─ 🚀 General Trading Chat
├─ 📊 Signal Discussions
├─ 🎯 Strategy Sharing
├─ 💡 Tips & Tricks
└─ 📚 Educational Content

**Active Traders Online: 234**
**Messages Today: 1,247**

**Choose a channel:**
    """

    from bot_telegram.keyboards import get_chat_main_keyboard
    await update.message.reply_text(
        text,
        reply_markup=get_chat_main_keyboard(),
        parse_mode=ParseMode.MARKDOWN
    )

async def _toggle_auto_trading(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Toggle auto trading for user"""
    user_id = update.effective_user.id

    try:
        user = await bot_manager.user_manager.get_user(user_id)
        if not user:
            await update.message.reply_text("❌ User not found.")
            return

        if not user.trading_enabled:
            await update.message.reply_text(
                "⚠️ Trading is not enabled. Configure API keys first using /register"
            )
            return

        # Toggle auto trading
        new_auto_trading = not user.auto_trading

        await bot_manager.user_manager.update_trading_preferences(user_id, {
            'auto_trading': new_auto_trading
        })

        status = "ON" if new_auto_trading else "OFF"
        emoji = "✅" if new_auto_trading else "❌"

        text = f"""
{emoji} **Auto Trading {status}**

{'🚀 Auto trading is now active! The bot will automatically execute trades based on your selected strategies.' if new_auto_trading else '🛑 Auto trading has been disabled. You can still receive signals and trade manually.'}

**Current Settings:**
├─ ⚡ Auto Trading: {status}
├─ 💰 Default Amount: ${user.default_trading_amount}
├─ ⚠️ Risk Level: {user.default_risk_level.title()}
└─ 🎯 Active Strategies: {len(user.enabled_strategies)}

**Quick Actions:**
        """

        keyboard = [
            [InlineKeyboardButton("⚙️ Trading Settings", callback_data="trading_settings")],
            [InlineKeyboardButton("🎯 Manage Strategies", callback_data="manage_strategies")],
            [InlineKeyboardButton("📊 View Performance", callback_data="user_portfolio")],
            [InlineKeyboardButton("🔙 Back", callback_data="user_main")]
        ]

        await update.message.reply_text(
            text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode=ParseMode.MARKDOWN
        )

    except Exception as e:
        logger.error(f"❌ Error toggling auto trading: {e}")
        await update.message.reply_text("❌ Could not toggle auto trading.")

async def _send_account_settings(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Send account settings interface"""
    user_id = update.effective_user.id

    try:
        user = await bot_manager.user_manager.get_user(user_id)
        is_premium = await bot_manager.user_manager.is_premium(user_id)
        is_vip = await bot_manager.user_manager.is_vip(user_id)

        premium_status = "👑 VIP" if is_vip else "💎 Premium" if is_premium else "🆓 Free"

        text = f"""
⚙️ **ACCOUNT SETTINGS** ⚙️

👤 **Profile:**
├─ 📛 Name: {user.first_name} {user.last_name or ''}
├─ 🆔 User ID: {user.user_id}
├─ 🎖️ Status: {premium_status}
├─ 🌐 Language: {user.language.upper()}
└─ 📅 Member Since: {user.created_at.strftime('%Y-%m-%d')}

🔧 **Trading Settings:**
├─ 🔑 API Keys: {'✅' if user.api_keys_configured else '❌'} Configured
├─ 💹 Trading: {'✅' if user.trading_enabled else '❌'} Enabled
├─ ⚡ Auto Trading: {'✅' if user.auto_trading else '❌'}
├─ 💰 Default Amount: ${user.default_trading_amount}
└─ ⚠️ Risk Level: {user.default_risk_level.title()}

**Account Actions:**
        """

        keyboard = [
            [InlineKeyboardButton("🔑 Manage API Keys", callback_data="manage_api_keys")],
            [InlineKeyboardButton("⚙️ Trading Preferences", callback_data="trading_preferences")],
            [InlineKeyboardButton("🌐 Language Settings", callback_data="language_settings")],
            [InlineKeyboardButton("💎 Upgrade Account", callback_data="upgrade_vip")] if not is_premium else [],
            [InlineKeyboardButton("🗑️ Delete Account", callback_data="delete_account")],
            [InlineKeyboardButton("🔙 Back", callback_data="user_main")]
        ]

        # Remove empty lists
        keyboard = [row for row in keyboard if row]

        await update.message.reply_text(
            text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode=ParseMode.MARKDOWN
        )

    except Exception as e:
        logger.error(f"❌ Error sending account settings: {e}")
        await update.message.reply_text("❌ Could not load account settings.")

# =================================================================================
# 🔐 INPUT PROCESSING FUNCTIONS
# =================================================================================

async def _process_api_key_input(update: Update, context: ContextTypes.DEFAULT_TYPE, api_key: str):
    """Process API key input"""
    user_id = update.effective_user.id

    # Store API key temporarily
    context.user_data['temp_api_key'] = api_key
    context.user_data['state'] = 'waiting_api_secret'

    await update.message.reply_text(
        "🔑 API Key received!\n\n"
        "Now please enter your **API Secret**:",
        parse_mode=ParseMode.MARKDOWN
    )

async def _process_api_secret_input(update: Update, context: ContextTypes.DEFAULT_TYPE, api_secret: str):
    """Process API secret input"""
    user_id = update.effective_user.id
    exchange = context.user_data.get('exchange', 'kucoin')

    context.user_data['temp_api_secret'] = api_secret

    if exchange.lower() == 'kucoin':
        context.user_data['state'] = 'waiting_api_passphrase'
        await update.message.reply_text(
            "🔑 API Secret received!\n\n"
            "Finally, please enter your **API Passphrase** (KuCoin only):",
            parse_mode=ParseMode.MARKDOWN
        )
    else:
        # Binance doesn't need passphrase
        await _finalize_api_setup(update, context, None)

async def _process_api_passphrase_input(update: Update, context: ContextTypes.DEFAULT_TYPE, passphrase: str):
    """Process API passphrase input"""
    await _finalize_api_setup(update, context, passphrase)

async def _finalize_api_setup(update: Update, context: ContextTypes.DEFAULT_TYPE, passphrase: Optional[str]):
    """Finalize API key setup"""
    user_id = update.effective_user.id
    exchange = context.user_data.get('exchange', 'kucoin')

    try:
        api_data = {
            'api_key': context.user_data.get('temp_api_key'),
            'api_secret': context.user_data.get('temp_api_secret'),
        }

        if passphrase:
            api_data['passphrase'] = passphrase

        # Store encrypted API keys
        success = await bot_manager.user_manager.store_api_keys(
            user_id, exchange, api_data
        )

        if success:
            text = f"""
✅ **API Keys Configured Successfully!**

🎉 Your {exchange.title()} account is now connected!

🚀 **What's Next:**
├─ 💹 Enable Trading Strategies
├─ 💰 Set Trading Amount
├─ ⚡ Configure Auto Trading
└─ 🎯 Start Trading!

**Your trading journey begins now!**
            """

            keyboard = [
                [InlineKeyboardButton("🎯 Configure Strategies", callback_data="configure_strategies")],
                [InlineKeyboardButton("💰 Set Trading Amount", callback_data="set_trading_amount")],
                [InlineKeyboardButton("📊 Go to Dashboard", callback_data="user_main")]
            ]

            await update.message.reply_text(
                text,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode=ParseMode.MARKDOWN
            )
        else:
            await update.message.reply_text(
                "❌ Failed to save API keys. Please try again or contact support."
            )

        # Clear temporary data
        context.user_data.clear()

    except Exception as e:
        logger.error(f"❌ Error finalizing API setup: {e}")
        await update.message.reply_text(
            "❌ An error occurred while setting up your API keys. Please try again."
        )
        context.user_data.clear()

async def _process_trade_amount_input(update: Update, context: ContextTypes.DEFAULT_TYPE, amount_text: str):
    """Process trading amount input"""
    user_id = update.effective_user.id

    try:
        amount = float(amount_text.replace('$', '').replace(',', ''))

        if amount < 10:
            await update.message.reply_text(
                "⚠️ Minimum trading amount is $10. Please enter a higher amount:"
            )
            return

        if amount > 10000:
            await update.message.reply_text(
                "⚠️ Maximum trading amount is $10,000. Please enter a lower amount:"
            )
            return

        # Update user preferences
        await bot_manager.user_manager.update_trading_preferences(user_id, {
            'default_trading_amount': amount
        })

        await update.message.reply_text(
            f"✅ Trading amount set to **${amount:,.2f}**\n\n"
            "This amount will be used for each trade unless specified otherwise.",
            parse_mode=ParseMode.MARKDOWN
        )

        context.user_data.clear()

    except ValueError:
        await update.message.reply_text(
            "❌ Invalid amount format. Please enter a number (e.g., 100 or 100.50):"
        )

@admin_required
async def _process_broadcast_message(update: Update, context: ContextTypes.DEFAULT_TYPE, message: str):
    """Process broadcast message from admin"""
    user_id = update.effective_user.id

    try:
        # Get broadcast target
        target = context.user_data.get('broadcast_target', 'all')

        # Send broadcast
        sent_count, failed_count = await bot_manager.broadcast_message(message, target)

        await update.message.reply_text(
            f"📤 **Broadcast Sent!**\n\n"
            f"✅ Sent: {sent_count} users\n"
            f"❌ Failed: {failed_count} users\n"
            f"🎯 Target: {target}"
        )

        context.user_data.clear()

    except Exception as e:
        logger.error(f"❌ Error processing broadcast: {e}")
        await update.message.reply_text("❌ Broadcast failed.")
        context.user_data.clear()

# =================================================================================
# 🔧 HELPER FUNCTIONS
# =================================================================================

async def _get_latest_signals(limit: int = 5) -> List[Dict]:
    """Get latest trading signals"""
    # This would integrate with your signal generation system
    # For now, return mock data
    return [
        {
            'symbol': 'BTC/USDT',
            'action': 'Strong Buy',
            'price': 44250.00,
            'confidence': 85,
            'trend': 'bullish'
        },
        {
            'symbol': 'ETH/USDT',
            'action': 'Buy',
            'price': 2650.00,
            'confidence': 78,
            'trend': 'bullish'
        },
        {
            'symbol': 'ADA/USDT',
            'action': 'Sell',
            'price': 0.45,
            'confidence': 72,
            'trend': 'bearish'
        }
    ]

async def _send_analytics_dashboard(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Send analytics dashboard for admins"""
    try:
        # Get system stats
        stats = await bot_manager.get_system_health()
        user_report = await bot_manager.user_manager.get_user_activity_report()
        trading_stats = await bot_manager.trading_engine.get_stats()

        text = f"""
📊 **SYSTEM ANALYTICS** 📊

👥 **User Statistics:**
├─ 📊 Total Users: {user_report.get('total_users', 0)}
├─ 🟢 Active Users: {user_report.get('active_users', 0)}
├─ 🆕 New Users (30d): {user_report.get('new_users', 0)}
├─ 💎 Premium Users: {user_report.get('premium_users', 0)}
└─ 👑 VIP Users: {user_report.get('vip_users', 0)}

💹 **Trading Statistics:**
├─ 🔢 Total Trades: {trading_stats.get('total_trades', 0)}
├─ 💰 Total Volume: {format_currency(trading_stats.get('total_volume', 0))}
├─ 📈 Total PnL: {format_currency(trading_stats.get('total_pnl', 0))}
├─ 🎯 Win Rate: {format_percentage(trading_stats.get('win_rate', 0))}
└─ 📊 Open Positions: {trading_stats.get('open_positions', 0)}

🖥️ **System Health:**
├─ 🤖 Bot Status: {stats.get('bot_status', 'unknown').title()}
├─ 💹 Trading Engine: {stats.get('trading_engine', 'unknown').title()}
├─ 🗄️ Database: {stats.get('database', 'unknown').title()}
├─ ⏱️ Uptime: {stats.get('uptime_hours', 0):.1f}h
└─ 💾 Memory: {stats.get('memory_usage', 0):.1f}MB

**System Management:**
        """

        keyboard = [
            [InlineKeyboardButton("🔄 Refresh Stats", callback_data="refresh_analytics")],
            [InlineKeyboardButton("👥 User Management", callback_data="ceo_users")],
            [InlineKeyboardButton("💹 Trading Controls", callback_data="trading_controls")],
            [InlineKeyboardButton("🔙 Back to CEO", callback_data="ceo_main")]
        ]

        await update.message.reply_text(
            text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode=ParseMode.MARKDOWN
        )

    except Exception as e:
        logger.error(f"❌ Error sending analytics dashboard: {e}")
        await update.message.reply_text("❌ Could not load analytics data.")

# Export handlers for main.py
__all__ = [
    'start_handler',
    'help_handler',
    'register_handler',
    'admin_handler',
    'trading_handler',
    'callback_handler',
    'message_handler',
    'set_bot_manager'
]