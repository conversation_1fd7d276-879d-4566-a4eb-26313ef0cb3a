#!/usr/bin/env python3
"""
🧪 TEST NIEUWE MENU SYSTEEM - Test alle nieuwe keyboards en handlers
================================================================

Test om te zien of alle nieuwe menu functies correct werken.
"""

import sys
import asyncio
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Setup basic logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_keyboards():
    """Test alle keyboard imports"""
    try:
        logger.info("🎨 Testing keyboard imports...")
        
        from bot_telegram.telegram.keyboards import (
            get_main_keyboard,
            get_trading_keyboard,
            get_portfolio_keyboard,
            get_admin_keyboard,
            get_market_data_keyboard,
            get_ai_assistant_keyboard,
            get_learning_center_keyboard,
            get_support_center_keyboard,
            get_settings_keyboard
        )
        
        # Test each keyboard
        keyboards = {
            'Main Menu': get_main_keyboard(),
            'Trading Hub': get_trading_keyboard(),
            'Portfolio': get_portfolio_keyboard(),
            'Admin Panel': get_admin_keyboard(),
            'Market Data': get_market_data_keyboard(),
            'AI Assistant': get_ai_assistant_keyboard(),
            'Learning Center': get_learning_center_keyboard(),
            'Support Center': get_support_center_keyboard(),
            'Settings': get_settings_keyboard()
        }
        
        for name, keyboard in keyboards.items():
            button_count = sum(len(row) for row in keyboard.inline_keyboard)
            logger.info(f"✅ {name}: {len(keyboard.inline_keyboard)} rows, {button_count} buttons")
        
        logger.info(f"✅ All {len(keyboards)} keyboards loaded successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Keyboard test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_handlers():
    """Test handler imports"""
    try:
        logger.info("🔧 Testing handler imports...")
        
        from bot_telegram.telegram.handlers import (
            start_handler,
            help_handler,
            register_handler,
            admin_handler,
            trading_handler,
            callback_handler,
            message_handler
        )
        
        handlers = [
            'start_handler',
            'help_handler', 
            'register_handler',
            'admin_handler',
            'trading_handler',
            'callback_handler',
            'message_handler'
        ]
        
        logger.info(f"✅ All {len(handlers)} handlers imported successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Handler test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_menu_structure():
    """Test menu structure and navigation"""
    try:
        logger.info("🗺️ Testing menu structure...")
        
        from bot_telegram.telegram.keyboards import get_main_keyboard
        
        # Get main menu
        main_menu = get_main_keyboard()
        
        # Count menu options
        total_buttons = sum(len(row) for row in main_menu.inline_keyboard)
        
        # Expected menu items
        expected_items = [
            "Trading Hub", "Portfolio", "Live Signals", "Market Data",
            "AI Assistant", "Auto Trading", "Community", "Learning",
            "Settings", "Premium", "Support", "About"
        ]
        
        logger.info(f"✅ Main menu has {total_buttons} buttons")
        logger.info(f"✅ Expected {len(expected_items)} menu items")
        
        # Test callback data structure
        callback_data = []
        for row in main_menu.inline_keyboard:
            for button in row:
                callback_data.append(button.callback_data)
        
        logger.info(f"✅ Callback data: {callback_data}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Menu structure test failed: {e}")
        return False

async def test_callback_routing():
    """Test callback routing logic"""
    try:
        logger.info("🔄 Testing callback routing...")
        
        # Test callback patterns
        test_callbacks = [
            "user_main",
            "trading_dashboard", 
            "market_data",
            "market_btc",
            "ai_assistant",
            "ai_trading_advice",
            "learning_center",
            "learn_basics",
            "support_center",
            "support_faq",
            "account_settings",
            "settings_notifications"
        ]
        
        # Check if callbacks follow expected patterns
        patterns = {
            'main': ['user_main'],
            'trading': ['trading_dashboard'],
            'market': [cb for cb in test_callbacks if cb.startswith('market_')],
            'ai': [cb for cb in test_callbacks if cb.startswith('ai_')],
            'learn': [cb for cb in test_callbacks if cb.startswith('learn_')],
            'support': [cb for cb in test_callbacks if cb.startswith('support_')],
            'settings': [cb for cb in test_callbacks if cb.startswith('settings_')]
        }
        
        for category, callbacks in patterns.items():
            logger.info(f"✅ {category.title()} callbacks: {len(callbacks)} items")
        
        logger.info("✅ Callback routing structure looks good!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Callback routing test failed: {e}")
        return False

async def main():
    """Run all menu tests"""
    logger.info("🧪 NIEUWE MENU SYSTEEM TEST")
    logger.info("=" * 50)
    
    tests = [
        ("Keyboard Imports", test_keyboards),
        ("Handler Imports", test_handlers),
        ("Menu Structure", test_menu_structure),
        ("Callback Routing", test_callback_routing),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 TEST: {test_name}")
        try:
            result = await test_func()
            results[test_name] = result
            status = "✅ PASS" if result else "❌ FAIL"
            logger.info(f"  Result: {status}")
        except Exception as e:
            logger.error(f"  ❌ EXCEPTION: {e}")
            results[test_name] = False
    
    # Results summary
    logger.info("\n" + "=" * 50)
    logger.info("📊 MENU SYSTEM TEST RESULTS:")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n🎯 OVERALL: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("\n🎉 ALL MENU TESTS PASSED!")
        logger.info("🚀 New menu system is ready!")
        logger.info("💡 You can now test the bot with: python main.py")
        return True
    else:
        logger.info(f"\n⚠️ {total - passed} tests failed.")
        logger.info("🔧 Please check the logs above for details.")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Test stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
