#!/usr/bin/env python3
"""
🧪 TEST BOT STARTUP - Test of de bot kan opstarten
================================================

Test script om te controleren of de bot correct kan opstarten en stoppen.
"""

import sys
import os
import asyncio
import logging
from pathlib import Path

# Voeg project root toe aan path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Setup basic logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_bot_startup():
    """Test of de bot kan opstarten."""
    try:
        logger.info("🧪 Starting bot startup test...")
        
        # Test 1: Import main bot class
        logger.info("📦 Testing main bot import...")
        from main import PrachtigeTradingBot
        logger.info("✅ Main bot class imported successfully")
        
        # Test 2: Create bot instance
        logger.info("🤖 Creating bot instance...")
        bot = PrachtigeTradingBot()
        logger.info("✅ Bot instance created successfully")
        
        # Test 3: Test database initialization
        logger.info("🗄️ Testing database initialization...")
        await bot.database_manager.initialize()
        logger.info("✅ Database initialized successfully")
        
        # Test 4: Test Telegram application setup
        logger.info("📱 Testing Telegram application setup...")
        if bot.application:
            logger.info("✅ Telegram application is configured")
        else:
            logger.warning("⚠️ Telegram application not configured")
        
        # Test 5: Test bot manager
        logger.info("🎯 Testing bot manager...")
        if bot.bot_manager:
            logger.info("✅ Bot manager is configured")
        else:
            logger.warning("⚠️ Bot manager not configured")
        
        # Cleanup
        logger.info("🧹 Cleaning up...")
        await bot.database_manager.close()
        
        logger.info("🎉 Bot startup test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Bot startup test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_quick_start():
    """Test een snelle start van de bot (zonder daadwerkelijk te starten)."""
    try:
        logger.info("🚀 Testing quick bot start...")
        
        # Import en maak bot instance
        from main import PrachtigeTradingBot
        bot = PrachtigeTradingBot()
        
        # Initialiseer alleen de database
        await bot.database_manager.initialize()
        
        # Test of alle componenten beschikbaar zijn
        components = {
            'Database Manager': bot.database_manager,
            'User Manager': bot.user_manager,
            'Bot Manager': bot.bot_manager,
            'Telegram Application': bot.application
        }
        
        logger.info("📊 Component status:")
        all_good = True
        for name, component in components.items():
            if component:
                logger.info(f"  ✅ {name}: OK")
            else:
                logger.warning(f"  ⚠️ {name}: Not initialized")
                all_good = False
        
        # Cleanup
        await bot.database_manager.close()
        
        if all_good:
            logger.info("🎉 All components are ready!")
        else:
            logger.warning("⚠️ Some components need attention")
        
        return all_good
        
    except Exception as e:
        logger.error(f"❌ Quick start test failed: {e}")
        return False

async def main():
    """Voer alle tests uit."""
    logger.info("🧪 TESTING BOT STARTUP FOR PRACHTIGE TRADING BOT")
    logger.info("=" * 60)
    
    # Test 1: Basic startup
    logger.info("\n📋 TEST 1: Basic Bot Startup")
    startup_success = await test_bot_startup()
    
    # Test 2: Quick start
    logger.info("\n📋 TEST 2: Quick Start Test")
    quickstart_success = await test_quick_start()
    
    # Resultaten
    logger.info("\n" + "=" * 60)
    logger.info("📊 TEST RESULTS:")
    logger.info(f"  🧪 Basic Startup: {'✅ PASS' if startup_success else '❌ FAIL'}")
    logger.info(f"  🚀 Quick Start: {'✅ PASS' if quickstart_success else '❌ FAIL'}")
    
    if startup_success and quickstart_success:
        logger.info("\n🎉 ALL TESTS PASSED! Bot is ready for production!")
        logger.info("💡 You can now run: python main.py")
        return True
    else:
        logger.info("\n⚠️ Some tests failed. Check the logs above.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
