#!/usr/bin/env python3
"""
🧪 TEST IMPORTS - Test alle belangrijke imports
==============================================

Test script om te controleren of alle modules correct kunnen worden geïmporteerd.
"""

import sys
import os
from pathlib import Path

# Voeg project root toe aan path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_import(module_name, description):
    """Test een import en print het resultaat."""
    try:
        exec(f"import {module_name}")
        print(f"✅ {description}: {module_name}")
        return True
    except Exception as e:
        print(f"❌ {description}: {module_name} - {e}")
        return False

def test_from_import(from_module, import_name, description):
    """Test een from...import en print het resultaat."""
    try:
        exec(f"from {from_module} import {import_name}")
        print(f"✅ {description}: from {from_module} import {import_name}")
        return True
    except Exception as e:
        print(f"❌ {description}: from {from_module} import {import_name} - {e}")
        return False

def main():
    """Voer alle import tests uit."""
    print("🧪 TESTING IMPORTS FOR PRACHTIGE TRADING BOT")
    print("=" * 50)
    
    success_count = 0
    total_count = 0
    
    # Test basis Python modules
    tests = [
        ("os", "Basic OS module"),
        ("sys", "Basic sys module"),
        ("asyncio", "Async IO module"),
        ("logging", "Logging module"),
        ("json", "JSON module"),
        ("datetime", "DateTime module"),
    ]
    
    print("\n📦 TESTING BASIC PYTHON MODULES:")
    for module, desc in tests:
        if test_import(module, desc):
            success_count += 1
        total_count += 1
    
    # Test externe dependencies
    external_tests = [
        ("telegram", "Python Telegram Bot"),
        ("dotenv", "Python dotenv"),
        ("pandas", "Pandas"),
        ("numpy", "NumPy"),
    ]
    
    print("\n📦 TESTING EXTERNAL DEPENDENCIES:")
    for module, desc in external_tests:
        if test_import(module, desc):
            success_count += 1
        total_count += 1
    
    # Test project modules
    project_tests = [
        ("config.config", "TELEGRAM_BOT_TOKEN", "Config module"),
        ("utils.logger", "setup_logging", "Logger utils"),
        ("utils.config", "Config", "Config utils"),
        ("core.bot_manager", "BotManager", "Bot Manager"),
        ("core.user_manager", "UserManager", "User Manager"),
        ("database.database_manager", "DatabaseManager", "Database Manager"),
        ("bot_telegram.telegram.keyboards", "get_main_keyboard", "Telegram Keyboards"),
        ("bot_telegram.telegram.handlers", "start_handler", "Telegram Handlers"),
    ]
    
    print("\n📦 TESTING PROJECT MODULES:")
    for from_module, import_name, desc in project_tests:
        if test_from_import(from_module, import_name, desc):
            success_count += 1
        total_count += 1
    
    # Test resultaten
    print("\n" + "=" * 50)
    print(f"📊 TEST RESULTS: {success_count}/{total_count} imports successful")
    
    if success_count == total_count:
        print("🎉 ALL IMPORTS SUCCESSFUL! Bot is ready to run.")
        return True
    else:
        failed_count = total_count - success_count
        print(f"⚠️  {failed_count} imports failed. Check dependencies and fix imports.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
