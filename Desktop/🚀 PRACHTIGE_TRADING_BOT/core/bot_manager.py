"""
🤖 BOT MANAGER - Central Management System
==========================================

Manages all bot operations, user interactions, and coordinates between
different components of the trading bot.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from telegram import Update, Bot
from telegram.ext import ContextTypes

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from bot_telegram.keyboards import (
    get_user_main_keyboard,
    get_ceo_main_keyboard,
    get_user_persistent_keyboard,
    get_ceo_persistent_keyboard
)
from utils.decorators import admin_required, premium_required
from utils.helpers import format_currency, format_percentage, get_user_language

logger = logging.getLogger(__name__)

@dataclass
class BotStats:
    """Statistieken van de bot"""
    total_users: int = 0
    active_users: int = 0
    premium_users: int = 0
    vip_users: int = 0
    total_trades: int = 0
    total_volume: float = 0.0
    total_pnl: float = 0.0
    uptime_percentage: float = 0.0
    trades_today: int = 0

class BotManager:
    """
    🎯 Centrale Bot Manager

    Beheert alle bot operaties:
    - User interactions
    - Command routing
    - State management
    - Statistics tracking
    - Admin operations
    """

    def __init__(self, user_manager, trading_engine, config):
        self.user_manager = user_manager
        self.trading_engine = trading_engine
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Bot statistics
        self.stats = BotStats()
        self.start_time = datetime.now()

        # Active user sessions
        self.active_sessions: Dict[int, Dict] = {}

        # Emergency modes
        self.emergency_stop = False
        self.maintenance_mode = False

        self.logger.info("🤖 Bot Manager geïnitialiseerd")

    async def handle_start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command"""
        user = update.effective_user
        chat_id = update.effective_chat.id

        try:
            # Register or get existing user
            db_user = await self.user_manager.get_or_create_user(
                user_id=user.id,
                username=user.username,
                first_name=user.first_name,
                last_name=user.last_name
            )

            # Check if user is admin
            is_admin = await self.user_manager.is_admin(user.id)

            # Send welcome message with appropriate keyboard
            if is_admin:
                welcome_text = self._get_admin_welcome_message(db_user)
                keyboard = get_ceo_persistent_keyboard()
                await update.message.reply_text(
                    welcome_text,
                    reply_markup=keyboard,
                    parse_mode='Markdown'
                )
                # Also send admin dashboard
                await self._send_admin_dashboard(update, context)
            else:
                welcome_text = self._get_user_welcome_message(db_user)
                keyboard = get_user_persistent_keyboard()
                await update.message.reply_text(
                    welcome_text,
                    reply_markup=keyboard,
                    parse_mode='Markdown'
                )
                # Send user dashboard
                await self._send_user_dashboard(update, context)

            # Update user activity
            await self.user_manager.update_last_activity(user.id)

            self.logger.info(f"👋 User {user.id} started the bot")

        except Exception as e:
            self.logger.error(f"❌ Error in start command: {e}")
            await update.message.reply_text(
                "🤖 Er is een fout opgetreden. Probeer het later opnieuw."
            )

    async def handle_callback_query(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle inline keyboard callbacks"""
        query = update.callback_query
        await query.answer()  # Acknowledge the callback

        user_id = query.from_user.id
        data = query.data

        try:
            # Check if user exists
            user = await self.user_manager.get_user(user_id)
            if not user:
                await query.edit_message_text("❌ Gebruiker niet gevonden. Gebruik /start om te beginnen.")
                return

            # Route callback based on data
            if data.startswith("ceo_"):
                await self._handle_admin_callback(query, context, data)
            elif data.startswith("user_"):
                await self._handle_user_callback(query, context, data)
            elif data.startswith("trading_"):
                await self._handle_trading_callback(query, context, data)
            elif data.startswith("signal_"):
                await self._handle_signal_callback(query, context, data)
            elif data.startswith("portfolio_"):
                await self._handle_portfolio_callback(query, context, data)
            else:
                await self._handle_general_callback(query, context, data)

            # Update user activity
            await self.user_manager.update_last_activity(user_id)

        except Exception as e:
            self.logger.error(f"❌ Error handling callback {data}: {e}")
            await query.edit_message_text("🤖 Er is een fout opgetreden.")

    async def _handle_admin_callback(self, query, context, data: str):
        """Handle admin-specific callbacks"""
        user_id = query.from_user.id

        # Verify admin permissions
        if not await self.user_manager.is_admin(user_id):
            await query.edit_message_text("❌ Geen admin rechten.")
            return

        if data == "ceo_main":
            await self._send_admin_dashboard_edit(query, context)
        elif data == "ceo_users":
            await self._send_user_management(query, context)
        elif data == "ceo_analytics":
            await self._send_analytics_dashboard(query, context)
        elif data == "ceo_emergency":
            await self._send_emergency_controls(query, context)
        elif data == "emergency_stop_all":
            await self._emergency_stop_all_bots(query, context)
        elif data == "emergency_close_all":
            await self._emergency_close_all_positions(query, context)
        # Add more admin callbacks...

    async def _handle_user_callback(self, query, context, data: str):
        """Handle regular user callbacks"""
        if data == "user_main":
            await self._send_user_dashboard_edit(query, context)
        elif data == "user_trading":
            await self._send_trading_interface(query, context)
        elif data == "user_portfolio":
            await self._send_portfolio_overview(query, context)
        elif data == "user_signals":
            await self._send_signals_dashboard(query, context)
        elif data == "upgrade_vip":
            await self._send_vip_upgrade_info(query, context)
        # Add more user callbacks...

    async def _handle_trading_callback(self, query, context, data: str):
        """Handle trading-related callbacks"""
        user_id = query.from_user.id

        # Check if trading is enabled for user
        user = await self.user_manager.get_user(user_id)
        if not user or not user.trading_enabled:
            await query.edit_message_text("❌ Trading is niet ingeschakeld voor jouw account.")
            return

        # Handle specific trading actions
        # Implementation depends on your trading logic
        pass

    def _get_admin_welcome_message(self, user) -> str:
        """Generate welcome message for admin users"""
        return f"""
👑 **WELKOM TERUG, ADMIN {user.first_name}!** 👑

🎯 **CEO COMMAND CENTER**
Je hebt volledige controle over het systeem.

🔥 **Quick Stats:**
├─ 👥 Actieve Users: {self.stats.active_users}
├─ 💰 Totale Volume: {format_currency(self.stats.total_volume)}
├─ 📊 Totale PnL: {format_currency(self.stats.total_pnl)}
└─ 🤖 Bot Uptime: {self.stats.uptime_percentage:.1f}%

**Kies een actie uit het menu hieronder:**
        """

    def _get_user_welcome_message(self, user) -> str:
        """Generate welcome message for regular users"""
        return f"""
🚀 **Welkom {user.first_name}!**

💹 **ADVANCED TRADING BOT**
Je persoonlijke AI trading assistent is klaar!

✨ **Beschikbare Features:**
├─ 🤖 AI-Powered Trading
├─ 📊 Live Market Signals
├─ 💰 Portfolio Management
├─ 📈 Advanced Analytics
└─ 💬 Trading Community

**Start je trading journey! 🎯**
        """

    async def _send_admin_dashboard(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Send admin dashboard"""
        await update.message.reply_text(
            self._get_admin_dashboard_text(),
            reply_markup=get_ceo_main_keyboard(),
            parse_mode='Markdown'
        )

    async def _send_admin_dashboard_edit(self, query, context):
        """Edit message to show admin dashboard"""
        await query.edit_message_text(
            self._get_admin_dashboard_text(),
            reply_markup=get_ceo_main_keyboard(),
            parse_mode='Markdown'
        )

    def _get_admin_dashboard_text(self) -> str:
        """Get admin dashboard text"""
        uptime = datetime.now() - self.start_time
        uptime_hours = uptime.total_seconds() / 3600

        return f"""
👑 **CEO COMMAND CENTER** 👑

🔥 **Live System Stats:**
├─ 👥 Total Users: {self.stats.total_users}
├─ 🟢 Active Now: {self.stats.active_users}
├─ 💎 Premium Users: {self.stats.premium_users}
├─ 👑 VIP Users: {self.stats.vip_users}
├─ 💰 Total Volume: {format_currency(self.stats.total_volume)}
├─ 📊 Total PnL: {format_currency(self.stats.total_pnl)}
├─ ⚡ Trades Today: {self.stats.trades_today}
└─ 🤖 Uptime: {uptime_hours:.1f}h

**Selecteer een actie:**
        """

    async def _send_user_dashboard(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Send user dashboard"""
        user_id = update.effective_user.id
        user_stats = await self.user_manager.get_user_stats(user_id)

        dashboard_text = f"""
💹 **TRADING DASHBOARD** 💹

📊 **Jouw Stats:**
├─ 💰 Balance: {format_currency(user_stats.get('balance', 0))}
├─ 📈 Open Posities: {user_stats.get('open_positions', 0)}
├─ 💸 Vandaag PnL: {format_currency(user_stats.get('daily_pnl', 0))}
├─ 🎯 Success Rate: {format_percentage(user_stats.get('success_rate', 0))}
└─ ⚡ Auto Trading: {'ON' if user_stats.get('auto_trading', False) else 'OFF'}

**Wat wil je doen?**
        """

        await update.message.reply_text(
            dashboard_text,
            reply_markup=get_user_main_keyboard(),
            parse_mode='Markdown'
        )

    async def update_stats(self):
        """Update bot statistics"""
        try:
            # Get user statistics
            self.stats.total_users = await self.user_manager.get_total_users()
            self.stats.active_users = await self.user_manager.get_active_users()
            self.stats.premium_users = await self.user_manager.get_premium_users()

            # Get trading statistics from trading engine
            trading_stats = await self.trading_engine.get_stats()
            self.stats.total_trades = trading_stats.get('total_trades', 0)
            self.stats.total_volume = trading_stats.get('total_volume', 0.0)
            self.stats.total_pnl = trading_stats.get('total_pnl', 0.0)
            self.stats.trades_today = trading_stats.get('trades_today', 0)

            # Calculate uptime
            uptime = datetime.now() - self.start_time
            total_hours = uptime.total_seconds() / 3600
            self.stats.uptime_percentage = min(100.0, (total_hours / 24) * 100)

        except Exception as e:
            self.logger.error(f"❌ Error updating stats: {e}")

    async def broadcast_message(self, message: str, user_type: str = "all"):
        """Broadcast message to users"""
        try:
            if user_type == "premium":
                users = await self.user_manager.get_premium_users_list()
            elif user_type == "vip":
                users = await self.user_manager.get_vip_users_list()
            else:
                users = await self.user_manager.get_all_users()

            sent_count = 0
            failed_count = 0

            for user in users:
                try:
                    # Send message implementation
                    # This would need the bot instance
                    sent_count += 1
                except Exception as e:
                    failed_count += 1
                    self.logger.warning(f"Failed to send broadcast to {user.user_id}: {e}")

            self.logger.info(f"📤 Broadcast sent: {sent_count} success, {failed_count} failed")
            return sent_count, failed_count

        except Exception as e:
            self.logger.error(f"❌ Error in broadcast: {e}")
            return 0, 0

    async def emergency_stop_all(self):
        """Emergency stop all bot operations"""
        self.emergency_stop = True
        await self.trading_engine.stop_all_trading()
        self.logger.warning("🚨 EMERGENCY STOP ACTIVATED")

    async def get_system_health(self) -> Dict[str, Any]:
        """Get system health status"""
        return {
            "bot_status": "online" if not self.emergency_stop else "emergency_stop",
            "trading_engine": await self.trading_engine.get_health_status(),
            "database": await self.user_manager.get_database_health(),
            "uptime_hours": (datetime.now() - self.start_time).total_seconds() / 3600,
            "active_sessions": len(self.active_sessions),
            "memory_usage": self._get_memory_usage(),
        }

    def _get_memory_usage(self) -> float:
        """Get current memory usage"""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024  # MB
        except ImportError:
            return 0.0
