"""
AI Trading Advisor - Verantwoordelijk voor het genereren van handelsaanbevelingen
gebaseerd op technische analyse en marktgegevens.
"""
import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
import ta
from enum import Enum

class TradeSignal(Enum):
    STRONG_BUY = "STERK_KOOP"
    BUY = "KOOP"
    NEUTRAL = "NEUTRAAL"
    SELL = "VERKOOP"
    STRONG_SELL = "STERK_VERKOOP"

class AITradingAdvisor:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    async def analyze_market(self, symbol: str, ohlcv_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Analyseer marktgegevens en geef een handelsaanbeveling.
        
        Args:
            symbol: Het te analyseren handelspaar (bijv. 'BTC/USDT')
            ohlcv_data: DataFrame met OHLCV-gegevens (Open, High, Low, Close, Volume)
            
        Returns:
            Dict met analyse resultaten en aanbeveling
        """
        try:
            # Voer technische analyse uit
            indicators = self._calculate_technical_indicators(ohlcv_data)
            
            # Genereer handelssignalen
            signals = self._generate_signals(ohlcv_data, indicators)
            
            # Bepaal het uiteindelijke handelssignaal
            recommendation = self._generate_recommendation(signals)
            
            # Genereer een gedetailleerde uitleg
            explanation = self._generate_explanation(recommendation, signals, indicators)
            
            return {
                'symbol': symbol,
                'timestamp': datetime.utcnow().isoformat(),
                'recommendation': recommendation.value,
                'confidence': signals['confidence'],
                'price': float(ohlcv_data['close'].iloc[-1]),
                'indicators': indicators,
                'explanation': explanation,
                'signals': signals
            }
            
        except Exception as e:
            self.logger.error(f"Fout bij het analyseren van markt: {str(e)}", exc_info=True)
            return {
                'error': str(e),
                'recommendation': TradeSignal.NEUTRAL.value,
                'confidence': 0.0,
                'explanation': 'Er is een fout opgetreden bij de analyse.'
            }
    
    def _calculate_technical_indicators(self, df: pd.DataFrame) -> Dict[str, float]:
        """Bereken technische indicatoren voor de gegeven marktgegevens."""
        # RSI (Relative Strength Index)
        rsi = ta.momentum.RSIIndicator(close=df['close'], window=14).rsi()
        
        # Moving Averages
        sma_20 = ta.trend.SMAIndicator(close=df['close'], window=20).sma_indicator()
        sma_50 = ta.trend.SMAIndicator(close=df['close'], window=50).sma_indicator()
        ema_12 = ta.trend.EMAIndicator(close=df['close'], window=12).ema_indicator()
        ema_26 = ta.trend.EMAIndicator(close=df['close'], window=26).ema_indicator()
        
        # MACD
        macd = ta.trend.MACD(close=df['close'])
        
        # Bollinger Bands
        bollinger = ta.volatility.BollingerBands(close=df['close'])
        
        # ATR (Average True Range) voor volatiliteit
        atr = ta.volatility.AverageTrueRange(
            high=df['high'], 
            low=df['low'], 
            close=df['close'], 
            window=14
        ).average_true_range()
        
        return {
            'rsi': float(rsi.iloc[-1]),
            'sma_20': float(sma_20.iloc[-1]),
            'sma_50': float(sma_50.iloc[-1]),
            'ema_12': float(ema_12.iloc[-1]),
            'ema_26': float(ema_26.iloc[-1]),
            'macd': float(macd.macd().iloc[-1]),
            'macd_signal': float(macd.macd_signal().iloc[-1]),
            'macd_hist': float(macd.macd_diff().iloc[-1]),
            'bb_upper': float(bollinger.bollinger_hband().iloc[-1]),
            'bb_middle': float(bollinger.bollinger_mavg().iloc[-1]),
            'bb_lower': float(bollinger.bollinger_lband().iloc[-1]),
            'atr': float(atr.iloc[-1])
        }
    
    def _generate_signals(self, df: pd.DataFrame, indicators: Dict[str, float]) -> Dict[str, Any]:
        """Genereer handelssignalen op basis van de technische indicatoren."""
        current_price = df['close'].iloc[-1]
        
        # RSI signalen
        rsi_signal = 0
        if indicators['rsi'] > 70:
            rsi_signal = -1  # Overbought
        elif indicators['rsi'] < 30:
            rsi_signal = 1   # Oversold
        
        # Moving Average signalen
        ma_signal = 0
        if indicators['ema_12'] > indicators['ema_26'] and df['close'].iloc[-1] > indicators['sma_50']:
            ma_signal = 1   # Bullish trend
        elif indicators['ema_12'] < indicators['ema_26'] and df['close'].iloc[-1] < indicators['sma_50']:
            ma_signal = -1  # Bearish trend
        
        # MACD signalen
        macd_signal = 1 if indicators['macd'] > indicators['macd_signal'] else -1
        
        # Bollinger Bands signalen
        bb_signal = 0
        if current_price < indicators['bb_lower'] * 1.02:  # Binnen 2% van de onderste band
            bb_signal = 1   # Mogelijke koopkans
        elif current_price > indicators['bb_upper'] * 0.98:  # Binnen 2% van de bovenste band
            bb_signal = -1  # Mogelijke verkoopkans
        
        # Bereken totaal aantal signalen
        total_signals = sum([abs(rsi_signal), abs(ma_signal), abs(macd_signal), abs(bb_signal)])
        if total_signals == 0:
            confidence = 0.0
        else:
            confidence = (abs(rsi_signal) + abs(ma_signal) + abs(macd_signal) + abs(bb_signal)) / total_signals
        
        return {
            'rsi': rsi_signal,
            'moving_averages': ma_signal,
            'macd': macd_signal,
            'bollinger_bands': bb_signal,
            'confidence': min(max(confidence, 0.0), 1.0)  # Zorg dat confidence tussen 0 en 1 blijft
        }
    
    def _generate_recommendation(self, signals: Dict[str, Any]) -> TradeSignal:
        """Genereer een handelsaanbeveling op basis van de signalen."""
        # Tel de buy en sell signalen
        buy_signals = sum([
            max(0, signals['rsi']),
            max(0, signals['moving_averages']),
            max(0, signals['macd']),
            max(0, signals['bollinger_bands'])
        ])
        
        sell_signals = abs(sum([
            min(0, signals['rsi']),
            min(0, signals['moving_averages']),
            min(0, signals['macd']),
            min(0, signals['bollinger_bands'])
        ]))
        
        # Bepaal het uiteindelijke signaal op basis van de signalen
        signal_strength = buy_signals - sell_signals
        confidence = signals['confidence']
        
        if signal_strength >= 2 and confidence > 0.6:
            return TradeSignal.STRONG_BUY
        elif signal_strength > 0 and confidence > 0.4:
            return TradeSignal.BUY
        elif signal_strength <= -2 and confidence > 0.6:
            return TradeSignal.STRONG_SELL
        elif signal_strength < 0 and confidence > 0.4:
            return TradeSignal.SELL
        else:
            return TradeSignal.NEUTRAL
    
    def _generate_explanation(self, recommendation: TradeSignal, 
                            signals: Dict[str, Any], 
                            indicators: Dict[str, float]) -> str:
        """Genereer een menselijke uitleg van de aanbeveling."""
        explanation = []
        
        # Voeg de aanbeveling toe
        if recommendation == TradeSignal.STRONG_BUY:
            explanation.append("💪🏼 STERKE KOOPAANBEVELING 💪🏼")
        elif recommendation == TradeSignal.BUY:
            explanation.append("🟢 KOOPAANBEVELING")
        elif recommendation == TradeSignal.STRONG_SELL:
            explanation.append("💪🏼 STERKE VERKOOPAANBEVELING 💪🏼")
        elif recommendation == TradeSignal.SELL:
            explanation.append("🔴 VERKOOPAANBEVELING")
        else:
            explanation.append("⚪ NEUTRAAL - GEEN DUIDELIJK SIGNAL")
        
        # Voeg details over de signalen toe
        explanation.append("\n📊 TECHNISCHE ANALYSE:")
        
        # RSI uitleg
        rsi = indicators['rsi']
        if rsi > 70:
            explanation.append(f"• RSI {rsi:.1f} - Overbought (boven 70)")
        elif rsi < 30:
            explanation.append(f"• RSI {rsi:.1f} - Oversold (onder 30)")
        else:
            explanation.append(f"• RSI {rsi:.1f} - Neutraal")
        
        # Moving Averages uitleg
        if indicators['ema_12'] > indicators['ema_26']:
            explanation.append("• EMA 12 > EMA 26 - Opwaartse trend")
        else:
            explanation.append("• EMA 12 < EMA 26 - Neerwaartse trend")
            
        if indicators['macd'] > indicators['macd_signal']:
            explanation.append("• MACD boven signaallijn - Bullish momentum")
        else:
            explanation.append("• MACD onder signaallijn - Bearish momentum")
        
        # Voeg het vertrouwen toe
        explanation.append(f"\n🛡️ VERTROUWEN: {signals['confidence']*100:.1f}%")
        
        # Voeg een waarschuwing toe
        explanation.append("\n⚠️ LET OP: Dit is geen financieel advies. Handel op eigen risico.")
        
        return "\n".join(explanation)