    async def get_api_keys(self, user_id: int, exchange: str) -> Optional[Dict[str, str]]:
        """Get decrypted API keys for user"""
        try:
            encrypted_data = await self.db.get_user_api_keys(user_id, exchange)
            if not encrypted_data:
                return None
            
            # Decrypt data
            decrypted_data = {}
            for key, encrypted_value in encrypted_data.items():
                if encrypted_value:
                    decrypted_data[key] = decrypt_data(encrypted_value)
            
            return decrypted_data
            
        except Exception as e:
            self.logger.error(f"❌ Fout bij ophalen API keys {user_id}: {e}")
            return None
    
    async def delete_api_keys(self, user_id: int, exchange: str) -> bool:
        """Delete API keys for user"""
        try:
            await self.db.delete_user_api_keys(user_id, exchange)
            
            # Check if user has any other API keys configured
            remaining_keys = await self.db.get_user_all_api_keys(user_id)
            
            if not remaining_keys:
                await self.update_user(user_id, {
                    'api_keys_configured': False,
                    'trading_enabled': False
                })
            
            self.logger.info(f"🗑️ API keys verwijderd voor user {user_id} ({exchange})")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Fout bij verwijderen API keys {user_id}: {e}")
            return False
    
    async def update_trading_preferences(self, user_id: int, preferences: Dict[str, Any]) -> bool:
        """Update user's trading preferences"""
        try:
            allowed_fields = [
                'default_trading_amount',
                'default_risk_level',
                'auto_trading',
                'enabled_strategies',
                'language',
                'timezone'
            ]
            
            # Filter only allowed fields
            updates = {k: v for k, v in preferences.items() if k in allowed_fields}
            
            if updates:
                await self.update_user(user_id, updates)
                self.logger.info(f"⚙️ Trading preferences geüpdatet voor user {user_id}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"❌ Fout bij updaten preferences {user_id}: {e}")
            return False
    
    async def update_trading_stats(self, user_id: int, trade_result: Dict[str, Any]) -> bool:
        """Update user's trading statistics"""
        try:
            user = await self.get_user(user_id)
            if not user:
                return False
            
            # Calculate new stats
            new_total_trades = user.total_trades + 1
            new_total_volume = user.total_volume + trade_result.get('volume', 0)
            new_total_pnl = user.total_pnl + trade_result.get('pnl', 0)
            
            new_winning_trades = user.winning_trades
            if trade_result.get('pnl', 0) > 0:
                new_winning_trades += 1
            
            updates = {
                'total_trades': new_total_trades,
                'winning_trades': new_winning_trades,
                'total_volume': new_total_volume,
                'total_pnl': new_total_pnl,
            }
            
            await self.update_user(user_id, updates)
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Fout bij updaten trading stats {user_id}: {e}")
            return False
    
    async def get_user_stats(self, user_id: int) -> Dict[str, Any]:
        """Get comprehensive user statistics"""
        try:
            user = await self.get_user(user_id)
            if not user:
                return {}
            
            # Calculate derived stats
            win_rate = 0.0
            if user.total_trades > 0:
                win_rate = user.winning_trades / user.total_trades
            
            avg_trade_size = 0.0
            if user.total_trades > 0:
                avg_trade_size = user.total_volume / user.total_trades
            
            # Get recent activity
            recent_trades = await self.db.get_user_recent_trades(user_id, limit=10)
            
            return {
                'user_id': user.user_id,
                'role': user.role.value,
                'member_since': user.created_at,
                'last_activity': user.last_activity,
                'trading_enabled': user.trading_enabled,
                'auto_trading': user.auto_trading,
                
                # Trading stats
                'total_trades': user.total_trades,
                'winning_trades': user.winning_trades,
                'losing_trades': user.total_trades - user.winning_trades,
                'win_rate': win_rate,
                'total_volume': user.total_volume,
                'total_pnl': user.total_pnl,
                'avg_trade_size': avg_trade_size,
                
                # Current status
                'balance': await self._get_user_balance(user_id),
                'open_positions': await self._get_user_open_positions(user_id),
                'daily_pnl': await self._get_user_daily_pnl(user_id),
                
                # Premium status
                'is_premium': await self.is_premium(user_id),
                'is_vip': await self.is_vip(user_id),
                'premium_until': user.premium_until,
                'vip_until': user.vip_until,
                
                # Recent activity
                'recent_trades': recent_trades,
                'enabled_strategies': user.enabled_strategies,
            }
            
        except Exception as e:
            self.logger.error(f"❌ Fout bij ophalen user stats {user_id}: {e}")
            return {}
    
    async def _get_user_balance(self, user_id: int) -> float:
        """Get user's current balance"""
        # This would integrate with the trading engine
        # For now, return a placeholder
        return 1000.0
    
    async def _get_user_open_positions(self, user_id: int) -> int:
        """Get number of user's open positions"""
        # This would integrate with the trading engine
        return 0
    
    async def _get_user_daily_pnl(self, user_id: int) -> float:
        """Get user's daily PnL"""
        # This would integrate with the trading engine
        return 0.0
    
    async def suspend_user(self, user_id: int, reason: str = "") -> bool:
        """Suspend a user"""
        try:
            await self.update_user(user_id, {
                'status': UserStatus.SUSPENDED.value,
                'trading_enabled': False,
                'suspension_reason': reason,
                'suspended_at': datetime.now()
            })
            
            self.logger.warning(f"⚠️ User {user_id} suspended: {reason}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Fout bij suspenderen user {user_id}: {e}")
            return False
    
    async def ban_user(self, user_id: int, reason: str = "") -> bool:
        """Ban a user"""
        try:
            await self.update_user(user_id, {
                'status': UserStatus.BANNED.value,
                'trading_enabled': False,
                'ban_reason': reason,
                'banned_at': datetime.now()
            })
            
            # Clear API keys
            await self.db.delete_all_user_api_keys(user_id)
            
            self.logger.warning(f"🚫 User {user_id} banned: {reason}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Fout bij bannen user {user_id}: {e}")
            return False
    
    async def unban_user(self, user_id: int) -> bool:
        """Unban a user"""
        try:
            await self.update_user(user_id, {
                'status': UserStatus.ACTIVE.value,
                'ban_reason': None,
                'banned_at': None
            })
            
            self.logger.info(f"✅ User {user_id} unbanned")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Fout bij unbannen user {user_id}: {e}")
            return False
    
    # Admin statistics methods
    async def get_total_users(self) -> int:
        """Get total number of users"""
        return await self.db.count_users()
    
    async def get_active_users(self, hours: int = 24) -> int:
        """Get number of active users in last X hours"""
        since = datetime.now() - timedelta(hours=hours)
        return await self.db.count_active_users(since)
    
    async def get_premium_users(self) -> int:
        """Get number of premium users"""
        return await self.db.count_premium_users()
    
    async def get_vip_users(self) -> int:
        """Get number of VIP users"""
        return await self.db.count_vip_users()
    
    async def get_all_users(self) -> List[UserProfile]:
        """Get all users (admin only)"""
        try:
            users_data = await self.db.get_all_users()
            return [self._create_user_profile(user_data) for user_data in users_data]
        except Exception as e:
            self.logger.error(f"❌ Fout bij ophalen alle users: {e}")
            return []
    
    async def get_premium_users_list(self) -> List[UserProfile]:
        """Get list of premium users"""
        try:
            users_data = await self.db.get_premium_users_list()
            return [self._create_user_profile(user_data) for user_data in users_data]
        except Exception as e:
            self.logger.error(f"❌ Fout bij ophalen premium users: {e}")
            return []
    
    async def get_vip_users_list(self) -> List[UserProfile]:
        """Get list of VIP users"""
        try:
            users_data = await self.db.get_vip_users_list()
            return [self._create_user_profile(user_data) for user_data in users_data]
        except Exception as e:
            self.logger.error(f"❌ Fout bij ophalen VIP users: {e}")
            return []
    
    async def search_users(self, query: str) -> List[UserProfile]:
        """Search users by username or name"""
        try:
            users_data = await self.db.search_users(query)
            return [self._create_user_profile(user_data) for user_data in users_data]
        except Exception as e:
            self.logger.error(f"❌ Fout bij zoeken users: {e}")
            return []
    
    async def get_user_activity_report(self, days: int = 30) -> Dict[str, Any]:
        """Get user activity report"""
        try:
            since = datetime.now() - timedelta(days=days)
            
            return {
                'total_users': await self.get_total_users(),
                'new_users': await self.db.count_new_users(since),
                'active_users': await self.get_active_users(days * 24),
                'premium_users': await self.get_premium_users(),
                'vip_users': await self.get_vip_users(),
                'suspended_users': await self.db.count_suspended_users(),
                'banned_users': await self.db.count_banned_users(),
                'users_with_api_keys': await self.db.count_users_with_api_keys(),
                'auto_trading_enabled': await self.db.count_auto_trading_users(),
            }
            
        except Exception as e:
            self.logger.error(f"❌ Fout bij activity report: {e}")
            return {}
    
    async def cleanup_inactive_users(self, days: int = 365) -> int:
        """Clean up users inactive for X days"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            # Get inactive users
            inactive_users = await self.db.get_inactive_users(cutoff_date)
            
            cleaned_count = 0
            for user_data in inactive_users:
                user_id = user_data['user_id']
                
                # Don't delete premium/admin users
                if user_data.get('role') in ['premium', 'vip', 'admin']:
                    continue
                
                # Delete API keys
                await self.db.delete_all_user_api_keys(user_id)
                
                # Mark as cleaned (but keep record)
                await self.update_user(user_id, {
                    'status': 'inactive_cleaned',
                    'api_keys_configured': False,
                    'trading_enabled': False
                })
                
                cleaned_count += 1
            
            self.logger.info(f"🧹 Cleaned up {cleaned_count} inactive users")
            return cleaned_count
            
        except Exception as e:
            self.logger.error(f"❌ Fout bij cleanup inactive users: {e}")
            return 0
    
    async def get_database_health(self) -> str:
        """Get database health status"""
        try:
            # Test database connection
            await self.db.test_connection()
            return "healthy"
        except Exception as e:
            self.logger.error(f"❌ Database health check failed: {e}")
            return "unhealthy"
    
    def set_admin_user_ids(self, admin_ids: List[int]):
        """Set admin user IDs from config"""
        self.admin_user_ids = admin_ids
        self.logger.info(f"👑 Admin users configured: {len(admin_ids)}")
    
    async def create_session(self, user_id: int) -> str:
        """Create user session"""
        try:
            session_token = secrets.token_urlsafe(32)
            session = UserSession(
                user_id=user_id,
                token=session_token,
                created_at=datetime.now(),
                expires_at=datetime.now() + timedelta(hours=24),
                last_activity=datetime.now()
            )
            
            self.active_sessions[user_id] = session
            await self.db.create_session(session)
            
            return session_token
            
        except Exception as e:
            self.logger.error(f"❌ Fout bij maken session {user_id}: {e}")
            return ""
    
    async def validate_session(self, user_id: int, token: str) -> bool:
        """Validate user session"""
        try:
            if user_id in self.active_sessions:
                session = self.active_sessions[user_id]
                if session.token == token and session.expires_at > datetime.now():
                    # Update last activity
                    session.last_activity = datetime.now()
                    return True
            
            # Check database
            session_data = await self.db.get_session(user_id, token)
            if session_data and session_data['expires_at'] > datetime.now():
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"❌ Fout bij valideren session {user_id}: {e}")
            return False
    
    async def destroy_session(self, user_id: int):
        """Destroy user session"""
        try:
            if user_id in self.active_sessions:
                del self.active_sessions[user_id]
            
            await self.db.delete_session(user_id)
            
        except Exception as e:
            self.logger.error(f"❌ Fout bij verwijderen session {user_id}: {e}")
