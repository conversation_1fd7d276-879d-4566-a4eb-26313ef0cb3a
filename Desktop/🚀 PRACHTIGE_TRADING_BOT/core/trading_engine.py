"""
🎯 TRADING ENGINE - Advanced Trading System (Continued)
"""

            # Binance
            if self.config.get('BINANCE_API_KEY'):
                self.exchanges['binance'] = ccxt.binance({
                    'apiKey': self.config.get('BINANCE_API_KEY'),
                    'secret': self.config.get('BINANCE_SECRET'),
                    'sandbox': self.config.get('TESTNET_MODE', False),
                    'enableRateLimit': True,
                })
                if not self.active_exchange:
                    self.active_exchange = self.exchanges['binance']
                self.logger.info("✅ Binance exchange verbonden")
            
            if not self.active_exchange:
                raise Exception("Geen exchange geconfigureerd")
            
            # Test connection
            await self._test_exchange_connection()
            
        except Exception as e:
            self.logger.error(f"❌ Fout bij initialiseren exchanges: {e}")
            raise
    
    async def _test_exchange_connection(self):
        """Test exchange connection"""
        try:
            balance = await self.active_exchange.fetch_balance()
            self.logger.info(f"✅ Exchange connectie getest - Balance: {balance.get('total', {})}")
        except Exception as e:
            self.logger.error(f"❌ Exchange connectie test gefaald: {e}")
            raise
    
    async def _load_strategies(self):
        """Load trading strategies"""
        try:
            # Basic strategies
            self.strategies['rsi'] = RSIStrategy(
                period=self.config.get('RSI_PERIOD', 14),
                oversold=self.config.get('RSI_OVERSOLD', 30),
                overbought=self.config.get('RSI_OVERBOUGHT', 70)
            )
            
            self.strategies['macd'] = MACDStrategy(
                fast_period=self.config.get('MACD_FAST', 12),
                slow_period=self.config.get('MACD_SLOW', 26),
                signal_period=self.config.get('MACD_SIGNAL', 9)
            )
            
            # AI Strategy (Premium)
            if self.config.get('OPENAI_API_KEY'):
                self.strategies['ai'] = AIStrategy(
                    api_key=self.config.get('OPENAI_API_KEY'),
                    model='gpt-4'
                )
            
            self.logger.info(f"✅ {len(self.strategies)} strategieën geladen")
            
        except Exception as e:
            self.logger.error(f"❌ Fout bij laden strategieën: {e}")
            raise
    
    async def _start_market_data_feeds(self):
        """Start real-time market data feeds"""
        try:
            # Supported trading pairs
            symbols = self.config.get('SUPPORTED_PAIRS', 'BTC/USDT,ETH/USDT').split(',')
            
            for symbol in symbols:
                symbol = symbol.strip()
                # Initialize empty dataframe for each symbol
                self.market_data[symbol] = pd.DataFrame()
                self.current_prices[symbol] = 0.0
            
            self.logger.info(f"✅ Market data feeds gestart voor {len(symbols)} pairs")
            
        except Exception as e:
            self.logger.error(f"❌ Fout bij starten market data feeds: {e}")
            raise
    
    async def _trading_loop(self):
        """Main trading loop"""
        while self.is_running:
            try:
                if not self.is_trading_enabled:
                    await asyncio.sleep(10)
                    continue
                
                # Update market data
                await self._update_market_data()
                
                # Generate signals from all active strategies
                signals = await self._generate_signals()
                
                # Process signals
                for signal in signals:
                    await self._process_signal(signal)
                
                # Update positions
                await self._update_positions()
                
                # Risk management checks
                await self._check_risk_limits()
                
                await asyncio.sleep(5)  # Main loop frequency
                
            except Exception as e:
                self.logger.error(f"❌ Fout in trading loop: {e}")
                await asyncio.sleep(30)
    
    async def _price_update_loop(self):
        """Real-time price update loop"""
        while self.is_running:
            try:
                symbols = list(self.market_data.keys())
                
                for symbol in symbols:
                    try:
                        ticker = await self.active_exchange.fetch_ticker(symbol)
                        self.current_prices[symbol] = ticker['last']
                    except Exception as e:
                        self.logger.warning(f"⚠️ Kon prijs niet ophalen voor {symbol}: {e}")
                
                await asyncio.sleep(1)  # Update prices every second
                
            except Exception as e:
                self.logger.error(f"❌ Fout in price update loop: {e}")
                await asyncio.sleep(10)
    
    async def _risk_monitoring_loop(self):
        """Risk monitoring loop"""
        while self.is_running:
            try:
                # Check portfolio risk
                portfolio_risk = await self.portfolio_manager.calculate_portfolio_risk()
                
                if portfolio_risk > self.config.get('MAX_PORTFOLIO_RISK', 5.0):
                    self.logger.warning(f"⚠️ Portfolio risico te hoog: {portfolio_risk}%")
                    await self._reduce_portfolio_risk()
                
                # Check individual position risks
                for position_id, position in self.positions.items():
                    await self._check_position_risk(position)
                
                await asyncio.sleep(30)  # Check risk every 30 seconds
                
            except Exception as e:
                self.logger.error(f"❌ Fout in risk monitoring: {e}")
                await asyncio.sleep(60)
    
    async def _update_market_data(self):
        """Update market data for all symbols"""
        try:
            for symbol in self.market_data.keys():
                try:
                    # Fetch OHLCV data
                    ohlcv = await self.active_exchange.fetch_ohlcv(
                        symbol, 
                        timeframe='1m', 
                        limit=100
                    )
                    
                    # Convert to DataFrame
                    df = pd.DataFrame(
                        ohlcv, 
                        columns=['timestamp', 'open', 'high', 'low', 'close', 'volume']
                    )
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                    df.set_index('timestamp', inplace=True)
                    
                    self.market_data[symbol] = df
                    
                except Exception as e:
                    self.logger.warning(f"⚠️ Kon market data niet updaten voor {symbol}: {e}")
                    
        except Exception as e:
            self.logger.error(f"❌ Fout bij updaten market data: {e}")
    
    async def _generate_signals(self) -> List[TradingSignal]:
        """Generate trading signals from all strategies"""
        signals = []
        
        try:
            for strategy_name, strategy in self.strategies.items():
                if strategy_name not in self.active_strategies:
                    continue
                
                for symbol, df in self.market_data.items():
                    if len(df) < 50:  # Need enough data
                        continue
                    
                    try:
                        signal = await strategy.generate_signal(symbol, df)
                        if signal:
                            signal.strategy_name = strategy_name
                            signals.append(signal)
                    except Exception as e:
                        self.logger.warning(f"⚠️ Fout bij genereren signaal {strategy_name}/{symbol}: {e}")
            
        except Exception as e:
            self.logger.error(f"❌ Fout bij genereren signalen: {e}")
        
        return signals
    
    async def _process_signal(self, signal: TradingSignal):
        """Process a trading signal"""
        try:
            # Check if signal meets minimum confidence
            if signal.confidence < 0.7:
                return
            
            # Risk management checks
            if not await self.risk_manager.validate_signal(signal):
                self.logger.info(f"🚫 Signaal geweigerd door risk management: {signal.symbol}")
                return
            
            # Calculate position size
            position_size = await self.risk_manager.calculate_position_size(
                signal.symbol, 
                signal.entry_price
            )
            
            if position_size <= 0:
                return
            
            # Execute trade
            await self._execute_trade(signal, position_size)
            
        except Exception as e:
            self.logger.error(f"❌ Fout bij verwerken signaal: {e}")
    
    async def _execute_trade(self, signal: TradingSignal, position_size: float):
        """Execute a trade based on signal"""
        try:
            order = await self.active_exchange.create_market_order(
                symbol=signal.symbol,
                side=signal.side.value,
                amount=position_size,
            )
            
            # Create position record
            position = Position(
                id=order['id'],
                user_id=0,  # System trades
                symbol=signal.symbol,
                side=signal.side,
                size=position_size,
                entry_price=order['average'] or signal.entry_price,
                current_price=self.current_prices.get(signal.symbol, signal.entry_price),
                unrealized_pnl=0.0,
                stop_loss=signal.stop_loss,
                take_profit=signal.take_profit
            )
            
            self.positions[order['id']] = position
            
            # Update statistics
            self.stats['total_trades'] += 1
            self.stats['total_volume'] += position_size * position.entry_price
            
            self.logger.info(f"✅ Trade uitgevoerd: {signal.side.value} {position_size} {signal.symbol} @ {position.entry_price}")
            
        except Exception as e:
            self.logger.error(f"❌ Fout bij uitvoeren trade: {e}")
    
    async def _update_positions(self):
        """Update all open positions"""
        for position_id, position in list(self.positions.items()):
            try:
                # Update current price
                current_price = self.current_prices.get(position.symbol, position.current_price)
                position.current_price = current_price
                
                # Calculate unrealized PnL
                if position.side == OrderSide.BUY:
                    position.unrealized_pnl = (current_price - position.entry_price) * position.size
                else:
                    position.unrealized_pnl = (position.entry_price - current_price) * position.size
                
                # Check stop loss / take profit
                await self._check_exit_conditions(position)
                
            except Exception as e:
                self.logger.error(f"❌ Fout bij updaten positie {position_id}: {e}")
    
    async def _check_exit_conditions(self, position: Position):
        """Check if position should be closed"""
        try:
            should_close = False
            close_reason = ""
            
            # Check stop loss
            if position.stop_loss:
                if position.side == OrderSide.BUY and position.current_price <= position.stop_loss:
                    should_close = True
                    close_reason = "Stop Loss"
                elif position.side == OrderSide.SELL and position.current_price >= position.stop_loss:
                    should_close = True
                    close_reason = "Stop Loss"
            
            # Check take profit
            if position.take_profit:
                if position.side == OrderSide.BUY and position.current_price >= position.take_profit:
                    should_close = True
                    close_reason = "Take Profit"
                elif position.side == OrderSide.SELL and position.current_price <= position.take_profit:
                    should_close = True
                    close_reason = "Take Profit"
            
            if should_close:
                await self._close_position(position, close_reason)
                
        except Exception as e:
            self.logger.error(f"❌ Fout bij checken exit conditions: {e}")
    
    async def _close_position(self, position: Position, reason: str = "Manual"):
        """Close a position"""
        try:
            # Create opposite order
            opposite_side = OrderSide.SELL if position.side == OrderSide.BUY else OrderSide.BUY
            
            order = await self.active_exchange.create_market_order(
                symbol=position.symbol,
                side=opposite_side.value,
                amount=position.size,
            )
            
            # Calculate realized PnL
            realized_pnl = position.unrealized_pnl
            
            # Update statistics
            self.stats['total_pnl'] += realized_pnl
            if realized_pnl > 0:
                self.stats['winning_trades'] += 1
            
            # Remove position
            if position.id in self.positions:
                del self.positions[position.id]
            
            self.logger.info(f"✅ Positie gesloten: {position.symbol} PnL: {realized_pnl:.2f} Reden: {reason}")
            
        except Exception as e:
            self.logger.error(f"❌ Fout bij sluiten positie: {e}")
    
    async def _close_all_positions(self):
        """Close all open positions"""
        for position in list(self.positions.values()):
            await self._close_position(position, "Emergency Close")
    
    async def _cancel_all_orders(self):
        """Cancel all open orders"""
        try:
            for order_id in list(self.orders.keys()):
                try:
                    await self.active_exchange.cancel_order(order_id)
                    del self.orders[order_id]
                except Exception as e:
                    self.logger.warning(f"⚠️ Kon order {order_id} niet annuleren: {e}")
        except Exception as e:
            self.logger.error(f"❌ Fout bij annuleren orders: {e}")
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get trading statistics"""
        win_rate = 0.0
        if self.stats['total_trades'] > 0:
            win_rate = self.stats['winning_trades'] / self.stats['total_trades'] * 100
        
        return {
            'total_trades': self.stats['total_trades'],
            'winning_trades': self.stats['winning_trades'],
            'win_rate': win_rate,
            'total_volume': self.stats['total_volume'],
            'total_pnl': self.stats['total_pnl'],
            'trades_today': self.stats['trades_today'],
            'open_positions': len(self.positions),
            'active_strategies': len(self.active_strategies),
        }
    
    async def get_health_status(self) -> str:
        """Get trading engine health status"""
        if not self.is_running:
            return "offline"
        elif not self.is_trading_enabled:
            return "paused"
        elif len(self.exchanges) == 0:
            return "no_exchange"
        else:
            return "healthy"
    
    async def enable_strategy(self, strategy_name: str, user_id: int):
        """Enable a strategy for a user"""
        if strategy_name in self.strategies:
            if strategy_name not in self.active_strategies:
                self.active_strategies.append(strategy_name)
            self.logger.info(f"✅ Strategie {strategy_name} ingeschakeld voor user {user_id}")
            return True
        return False
    
    async def disable_strategy(self, strategy_name: str, user_id: int):
        """Disable a strategy for a user"""
        if strategy_name in self.active_strategies:
            self.active_strategies.remove(strategy_name)
            self.logger.info(f"🚫 Strategie {strategy_name} uitgeschakeld voor user {user_id}")
            return True
        return False
    
    async def get_current_price(self, symbol: str) -> Optional[float]:
        """Get current price for a symbol"""
        return self.current_prices.get(symbol)
    
    async def get_portfolio_value(self, user_id: int) -> float:
        """Get total portfolio value for a user"""
        # Implementation depends on how you track user-specific positions
        total_value = 0.0
        for position in self.positions.values():
            if position.user_id == user_id:
                total_value += position.size * position.current_price
        return total_value
    
    async def stop_all_trading(self):
        """Emergency stop all trading"""
        self.is_trading_enabled = False
        await self._close_all_positions()
        await self._cancel_all_orders()
        self.logger.warning("🚨 All trading stopped!")
